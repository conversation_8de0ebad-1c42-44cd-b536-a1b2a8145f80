import * as Yup from 'yup'

const MIN_PASSWORD_LENGTH = 12
const MAX_PASSWORD_LENGTH = 24

export const minPasswordLengthSchema = Yup.string().min(
  MIN_PASSWORD_LENGTH,
  'validators.password.min',
)

export const maxPasswordLengthSchema = Yup.string().max(
  MAX_PASSWORD_LENGTH,
  'validators.password.max',
)

export const isOneBigLetter = Yup.string().test('bigLetter', 'validators.password.passwordStrength', value =>
  // @ts-expect-error
  value.match(/[A-Z]/),
)

export const atLeastOneSpecialSign = Yup.string().test(
  'specialSign',
  'validators.password.passwordSpecialSign',
  (value: string) => {
    const specialSignRegex = /[^a-zA-Z0-9]/
    return value.match(specialSignRegex)
  },
)

export const atLeastOneSmallLetter = Yup.string().test(
  "smallLetter",
  "validators.password.minLengthSmallLetter",
  (value) =>
    // Ensure value exists before testing
    typeof value === "string" && /[a-z]/.test(value)
);


export const passwordRegisterSchema = Yup.string()
  .required('validators.password.required')
  .min(MIN_PASSWORD_LENGTH, 'validators.password.min')
  .max(MAX_PASSWORD_LENGTH, 'validators.password.max')
  .test('bigLetter', 'validators.password.passwordStrength', value =>
    // @ts-expect-error
    value.match(/[A-Z]/),
  )
  .test(
    'specialSign',
    'validators.password.passwordSpecialSign',
    (value: string) => {
      const specialSignRegex = /[^a-zA-Z0-9]/
      return value.match(specialSignRegex)
    },
  )

export const passwordConfirmRegisterSchema = (refPassword: unknown) =>
  Yup.string()
    .required('validators.password.required')
    .oneOf([refPassword, null], 'validators.password.passwordsMustMatch')

export const passwordSignInSchema = Yup.string()
  .required('validators.password.required')
  .min(MIN_PASSWORD_LENGTH, 'validators.password.min')

export const passwordConfirmRegisterShape = Yup.object().shape({
  password: passwordSignInSchema,
  passwordConfirm: Yup.string().oneOf(
    [Yup.ref('password'), ''],
    'validators.password.passwordsMustMatch',
  ),
})
