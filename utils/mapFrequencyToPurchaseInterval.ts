import { PurchaseInterval } from '@factory/saving-plan-settings'
import { Frequency } from '@store/saving-plan-store'

export const mapFrequencyToPurchaseInterval = (frequency?: Frequency) :PurchaseInterval => {
  switch (frequency) {
    case 'month':
      return PurchaseInterval.Month
    case 'quarter':
      return PurchaseInterval.Quarter
    case 'week':
    default:
      return PurchaseInterval.Week
  }
}

export const mapPurchaseIntervalToFrequency = (interval: PurchaseInterval): Frequency => {
  switch (interval) {
    case PurchaseInterval.Month:
      return 'month'
    case PurchaseInterval.Quarter:
      return 'quarter'
    case PurchaseInterval.Week:
    default:
      return 'week'
  }
}
