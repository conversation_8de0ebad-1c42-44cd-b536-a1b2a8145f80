import { PlanType, SavingPlanStoreProps } from '@store/saving-plan-store'
import { mapFrequencyToPurchaseInterval } from './mapFrequencyToPurchaseInterval'

export const mapSavingStoreDataToPostSaving = (savingPlanData:SavingPlanStoreProps)=> {
  const initialData = {
    name: savingPlanData.planName,
    plan_type: savingPlanData.selectedPlan,
    time_horizon: savingPlanData.timeHorizon,
    saving_enabled: true,
    target_id: savingPlanData.selectedTarget?.id
  }

  let postData

  switch (savingPlanData.selectedPlan) {
    case PlanType.Amount:
      postData = {
        ...initialData,
        amount_value: savingPlanData.fixedPlanValue,
        ignore_transaction_above: savingPlanData.ignoreTransactionsAbove,
      }
      break
    case PlanType.Percentage:
      postData = {
        ...initialData,
        percentage_value: savingPlanData.adjustedPlanPercentage,
        ignore_transaction_above: savingPlanData.ignoreTransactionsAbove,
      }
      break
    case PlanType.ReccuringPurchase:
      postData = {
        ...initialData,
        amount_value: savingPlanData.savingAmountAndFrequency.amount,
        purchase_interval: mapFrequencyToPurchaseInterval(savingPlanData?.savingAmountAndFrequency?.frequency),
      }
      break
  }
  return postData;
}
