### Definicja poprawnej PRki
- [ ] <PERSON><PERSON><PERSON><PERSON> się, że mergujemy do **dobrego BRANCHA**.
- [ ] Je<PERSON><PERSON> są wymagane zmiany w dokumentacji technicznej (https://app.clickup.com/24358951/v/dc/q7c17-792/q7c17-952) lub biz<PERSON><PERSON> (https://app.clickup.com/24358951/v/dc/q7c17-832/q7c17-852), to zostały one w niej zamieszczone .
- [ ] **PipeLine** w GitHub przeszedł pomyślnie.
- [ ] Posiada podlinkowany **Task z CU w opisie PR**.
- [ ] Została podlinkowana w **Tasku w CU jako komentarz**.
- [ ] Jeżeli jest to potrzebne, uwzględnia nowe **tłumaczenia**.
- [ ] Z<PERSON><PERSON><PERSON><PERSON> się, czy nie trzeba dodać zmian lub utworzyć tasków dla kogos.
- [ ] Wszystkie wrażliwe dane powinny być ukryte i nieprzechowywane w kodzie, a zamiast tego w CredentialVault przyjętym jako standardowy w organizacji RETI.
- [ ] Spełnia wszystkie UserStories w danej funkcjonalności.
- [ ] Czas poświęcony na realizację jest raportowany przez Wykonawcę w  realizowanym tasku.
- [ ] Zmiany są zgodny z designem z Figmry (marginesy, kolory, fonty, etc.).
- [ ] Zmiany zostały przetestowanie dla systemu Android i IOS.
