import {
  NotificationContainer,
  NotificationType,
} from '@ui/Notification/NotificationContainer'
import React, { useCallback, useState } from 'react'
import { useOnError } from '@hooks'
import { AppError, Error<PERSON>ey, errorHandlerService } from '@services'

export const RegisterErrorContainer = ({
  callback,
  onClear
}: {
  callback: () => void
  onClear?: () => void
}) => {
  const [show, setShow] = useState(false)
  const [errorMessage, setErrorMessage] = useState<AppError | undefined>(
    undefined,
  )
  useOnError(ErrorKey.RegistrationError, error => {
    if (error) {
      callback()
      setShow(true)
      setErrorMessage(error)
    } else {
      setShow(false)
      setErrorMessage(undefined)
    }
  })

  const onClearPress = useCallback(() => {
    onClear()
    errorHandlerService.clearError(ErrorKey.RegistrationError)
  }, [])

  const onPress = useCallback(() => {
    setShow(false)
    callback()
    onClearPress()
  }, [onClearPress])

  return (
    <NotificationContainer
      type={NotificationType.Error}
      label={errorMessage?.errorText}
      translate={errorMessage?.translate}
      show={show}
      onPress={onPress}
    />
  )
}
