import React, { useEffect, useState } from 'react'
import { View, Text, StyleSheet } from 'react-native'
import SvgVerificationChecked from '../../assets/check-verified.svg'
import SvgVerificationNonChecked from '../../assets/check-non-verified.svg'
import {
  atLeastOneSmallLetter,
  atLeastOneSpecialSign,
  isOneBigLetter,
  maxPasswordLengthSchema,
  minPasswordLengthSchema,
} from '@utils'
import { theme } from '@theme'
import { useTranslations } from '@hooks'

const DIMENSION = 25

interface IVerificationItem {
  isVerified: boolean
  label: string
}

export const VerificationLabel: React.FC<IVerificationItem> = ({
  isVerified = false,
  label,
}) => {
  const t = useTranslations()
  return (
    <View style={styles.row}>
      <View style={styles.rowIcon}>
        {isVerified ? (
          <SvgVerificationChecked width={DIMENSION} height={DIMENSION} />
        ) : (
          <SvgVerificationNonChecked width={DIMENSION} height={DIMENSION} />
        )}
      </View>
      <View>
        <Text style={styles.label}>{t(label)}</Text>
      </View>
    </View>
  )
}

export const VerificationList: React.FC<{ password?: string }> = ({
  password,
}) => {
  const [validationList, setValidationList] = useState<
    { isVerified: boolean; label: string }[]
  >([])

  useEffect(() => {
    const validate = async () => {
      return [
        {
          isVerified: await isOneBigLetter.isValid(password),
          label: 'validators.validationCheck.minLengthCapital',
        },{
          isVerified: await atLeastOneSmallLetter.isValid(password),
          label: 'validators.validationCheck.minLengthSmallLetter',
        },{
          isVerified: await atLeastOneSpecialSign.isValid(password),
          label: 'validators.validationCheck.requiredAtLeastOneSpecialCharacter',
        },
        {
          isVerified: await minPasswordLengthSchema.isValid(password),
          label: 'validators.validationCheck.minLength',
        },
        {
          isVerified: await maxPasswordLengthSchema.isValid(password),
          label: 'validators.validationCheck.maxLength',
        },
      ]
    }

    validate().then(data => {
      setValidationList(data)
    })

  }, [password])

  return (
    <View style={styles.container}>
      {validationList.map((element, index) => (
        <VerificationLabel
          key={`${index}-${element.label}`}
          label={element.label}
          isVerified={element.isVerified}
        />
      ))}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  rowIcon: {
    marginRight: 10,
  },
  label: {
    color: theme.colors.text,
  },
})
