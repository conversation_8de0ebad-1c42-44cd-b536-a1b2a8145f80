import React, { useCallback, useState } from 'react'
import style from 'components/SignInForm/SignInForm.styles'
import {
  useTranslations,
  useValidatorString,
  useValidatorObject,
  useIsKeyboardVisible,
} from '@hooks'
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'
import { TextInputField } from '@ui/TextInputField/TextInputField'
import {
  emailScheme,
  passwordConfirmRegisterShape,
  passwordRegisterSchema,
} from '@utils'
import { useGlobalSearchParams, useRouter } from 'expo-router'
import { ScrollView, View, StyleSheet } from 'react-native'
import { RegisterErrorContainer } from './RegisterErrorContainer'
import { VerificationList } from './VerificationList'
import useRegistrationStore from '@store/registration.store'

const SignUpForm = () => {
  const t = useTranslations()
  const params = useGlobalSearchParams()
  const router = useRouter()
  const [username, setUsername] = useState<string>(
    (params.email as string) || '',
  )
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setIsLoading] = useState(false)

  const {setLoginAndPassword}  = useRegistrationStore((state)=> ({setLoginAndPassword: state.setLoginAndPassword}))

  const [emailValidatorMessage, validateEmail, isValidEmail] =
    useValidatorString(emailScheme)

  const [passwordValidatorMessage, validatePassword, isValidPassword] =
    useValidatorString(passwordRegisterSchema)

  const [
    passwordConfirmValidatorMessage,
    validateConfirmPassword,
    isValidConfirmPassword,
  ] = useValidatorObject(passwordConfirmRegisterShape)

  const onUsernameChange = useCallback((username: string) => {
    validateEmail(username)
    setUsername(username)
  }, [])

  const onPasswordChange = useCallback((password: string) => {
    validatePassword(password)
    setPassword(password)
  }, [])

  const onConfirmPasswordChange = useCallback(
    (passwordConfirm: string) => {
      validateConfirmPassword({ password, passwordConfirm })
      setConfirmPassword(passwordConfirm)
    },
    [password],
  )

  const isVisible = useIsKeyboardVisible()

  const goToTerms = useCallback(()=> {
    setLoginAndPassword(username,password)
    router.push('/terms-of-use')
  }, [username, password])

  const isFormValid =
    isValidEmail(username) &&
    isValidPassword(password) &&
    password === confirmPassword

  return (
    <ScrollView
      showsVerticalScrollIndicator={true}
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.container}
    >
      <View>
        <RegisterErrorContainer
          callback={() => {
            setIsLoading(false)
          }}
        />
      </View>
      <View style={styles.inputContainers}>
        <TextInputField
          labelTranslation="signUp.inputs.username.label"
          value={username}
          onChangeText={onUsernameChange}
          keyboardType="email-address"
          autoComplete="email"
          autoCapitalize="none"
          errorMessage={emailValidatorMessage}
        />
        <TextInputField
          labelTranslation="signUp.inputs.password.label"
          value={password}
          onChangeText={onPasswordChange}
          autoCapitalize="none"
          errorMessage={passwordValidatorMessage}
          isPasswordField
        />
        <TextInputField
          labelTranslation="signUp.inputs.confirmPassword.label"
          value={confirmPassword}
          onChangeText={onConfirmPasswordChange}
          isPasswordField
          autoCapitalize="none"
          errorMessage={passwordConfirmValidatorMessage}
        />
        <View style={styles.checkVerificationContainer}>
          <VerificationList password={password} />
        </View>
      </View>
      {!isVisible && <View style={[styles.buttonContainer]}>
        <UiPrimaryButton
          loading={loading}
          disabled={!isFormValid}
          onPress={goToTerms}
          translationTitle="signUp.buttons.moveForward.label"
          containerStyle={style.mainActionButtonContainer}
          mode="contained"
        />
      </View>}
    </ScrollView>
  )
}

export default SignUpForm

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  containerWithKeyboard: {},
  inputContainers: {
    flex:3
  },
  buttonContainer: {
    flex:1,
    justifyContent: 'center',
    alignItems:'center',
  },
  checkVerificationContainer: {
    marginVertical: 5,
  },
})
