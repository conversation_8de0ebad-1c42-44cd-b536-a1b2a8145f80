import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSpring,
  withTiming,
} from 'react-native-reanimated'
import SVGCHeckCircle from '../../assets/check-circle.svg'
import { StyleSheet, View } from 'react-native'
import { theme } from '@theme'
import { UiTitle } from '@ui/typography/UiTitle'
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'
import { useEffect } from 'react'

const SVG_DIMENSION = 150
const TIMING = 300

interface IConfirmationScreen {
  message: string
  buttonTitle: string
  onButtonPress: () => void
}

export const ConfirmationScreen = ({
  message,
  buttonTitle,
  onButtonPress,
}: IConfirmationScreen) => {
  const sharedOpacity = useSharedValue(0)
  const scale = useSharedValue(0)

  useEffect(() => {
    sharedOpacity.value = withDelay(400, withTiming(1, { duration: TIMING }))
    scale.value = withSpring(1, { damping: 7, stiffness: 200 })
  }, [])

  const opacity = useAnimatedStyle(() => {
    return {
      opacity: sharedOpacity.value,
    }
  })

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }))

  return (
    <View style={styles.contentContainer}>
      <View style={styles.iconContainer}>
        <Animated.View style={[animatedStyle]}>
          <SVGCHeckCircle width={SVG_DIMENSION} height={SVG_DIMENSION} />
        </Animated.View>
        <Animated.View style={[styles.textContainer, opacity]}>
          <UiTitle translatedText={message} style={styles.textStyle} />
        </Animated.View>
      </View>
      <View style={styles.buttonContainer}>
        <UiPrimaryButton
          translationTitle={buttonTitle}
          mode="contained"
          onPress={onButtonPress}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  textStyle: {
    textAlign: 'center',
    fontSize: 32,
    lineHeight: 38,
  },
  textContainer: {
    paddingVertical: 20,
  },
  iconContainer: {
    flex: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingHorizontal: 20,
    backgroundColor: theme.colors.background,
  },
  buttonContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: '100%',
    marginBottom: 30,
  },
})
