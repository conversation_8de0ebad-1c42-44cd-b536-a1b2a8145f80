import React, { useState } from 'react'
import { Button } from 'react-native'
import * as ImagePicker from 'expo-image-picker'
import { MediaTypeOptions } from 'expo-image-picker'
import * as FileSystem from 'expo-file-system'
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'

const QUALITY  = 1;

interface Props {
  title: string;
  onCreateImage: (imagePath: string)=> void;
}

export default function CreatePhoto({title, onCreateImage}: Props) {
  const [images, setImages] = useState<string[]>([]);

  const takePicture = async () => {
    const result = await ImagePicker.launchCameraAsync({
      quality: QUALITY,
      allowsEditing: false,
      mediaTypes: MediaTypeOptions.Images
    });

    if (!result.canceled && result.assets[0].uri) {
      const savedUri = `${FileSystem.documentDirectory}${Date.now()}.jpg`;
      await FileSystem.copyAsync({
        from: result.assets[0].uri,
        to: savedUri
      });
      onCreateImage(savedUri)
    }
  };

  return (
    <UiPrimaryButton
      translationTitle={title}
      mode="outlined"
      onPress={takePicture}
      containerStyle={{
        width: 'auto',
      }}
    />
  )
}
