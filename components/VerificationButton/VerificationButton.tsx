import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'
import React, { useCallback } from 'react'
import { useRouter } from 'expo-router'

const VerificationButton: React.FC = () => {
  const router = useRouter()

  const onPress = useCallback(()=> {
    router.push('/(id-verification)')
  }, [router])

  return <UiPrimaryButton
    translationTitle="verification.buttons.common"
    mode="outlined"
    onPress={onPress}
    containerStyle={{
      width: 'auto',
    }}
  />
}

export default VerificationButton
