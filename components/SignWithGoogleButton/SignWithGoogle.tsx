import style from '../SignInForm/SignInForm.styles'
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'
import React from 'react'
import { theme } from '@theme'
import { Image } from 'expo-image'

export const SignWithGoogleButton = () => {
  return (
    <UiPrimaryButton
      icon={() => (<Image source={require('../../assets/google-button.png')} style={{ width: 30, height: 30 }} />)}
      translationTitle="signIn.buttons.signIn.signInByGoogleAccount"
      containerStyle={style.mainActionButtonContainer}
      mode="outlined"
      buttonStyle={{ borderColor: theme.colors.inputDefaultBorderColor }}
      textColor={theme.colors.gray}
    />
  )
}
