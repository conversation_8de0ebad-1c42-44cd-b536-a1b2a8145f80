import { Link } from 'expo-router'
import React, { FC, ReactNode } from 'react'
import { Card, Divider, Text } from 'react-native-paper'
import { style } from './AuthorizationForm.styles'

interface IBottomText {
  text: string
  linkText: string
  href: string
}

interface IAuthorizationFormProps {
  children: ReactNode
  title: string
  subtitle: string
  bottomText?: IBottomText
}

const AuthorizationForm: FC<IAuthorizationFormProps> = ({
  children,
  title,
  bottomText,
}) => (
  <Card mode="contained" style={style.main}>
    <Card.Content>{children}</Card.Content>
    {bottomText && (
      <>
        <Divider style={style.divider} />
        <Card.Content>
          <Text style={style.bottomText}>
            {`${bottomText.text} `}
            <Link href={bottomText.href} style={style.bottomTextAction}>
              {bottomText.linkText}
            </Link>
          </Text>
        </Card.Content>
      </>
    )}
  </Card>
)

export default AuthorizationForm
