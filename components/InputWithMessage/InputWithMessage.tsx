import React, { FC } from 'react'
import { Text, TextInput, TextInputProps } from 'react-native-paper'
import style from './InputWithMessage.styles'
import { View } from 'react-native'

interface InputWithMessageProps extends TextInputProps {
  message: string | undefined
}

const InputWithMessage: FC<InputWithMessageProps> = ({ message, ...rest }) => {
  return (
    <View>
      <TextInput {...rest} error={message !== undefined} />
      <View>
        {message && <Text style={style.errorMessage}>{message}</Text>}
      </View>
    </View>
  )
}

export default InputWithMessage
