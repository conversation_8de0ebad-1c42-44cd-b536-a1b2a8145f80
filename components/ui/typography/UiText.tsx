import { Text } from 'react-native-paper'
import { TextStyle } from 'react-native'
import { useTranslations } from '@hooks'
import { theme } from '@theme'
import type { VariantProp } from 'react-native-paper/src/components/Typography/types'

interface ITextProps {
  translatedText: string
  style?: TextStyle
  variant: VariantProp<any>
}

export const UiText = ({ translatedText, style, variant }: ITextProps) => {
  const t = useTranslations()
  return (
    <Text
      style={style} variant={variant}>
      {t(translatedText)}
    </Text>
  )
}
