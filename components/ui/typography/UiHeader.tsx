import { Text } from 'react-native-paper'
import { TextStyle } from 'react-native'
import { useTranslations } from '@hooks'
import { theme } from '@theme'

interface ITextProps {
  translatedText: string
  translate?:boolean
  style?: TextStyle
}

export const UiHeader = ({ translatedText, style }: ITextProps) => {
  const t = useTranslations()
  return (
    <Text
      style={[
        {
          marginTop: 25,
          fontSize: 32,
          lineHeight: 37,
          marginBottom: 40,
          textAlign: 'center',
          color: theme.colors.text
        },
        style,
      ]}
    >
      {t(translatedText)}
    </Text>
  )
}
