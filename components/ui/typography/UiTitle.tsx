import { Text } from 'react-native-paper'
import { TextStyle } from 'react-native'
import { useTranslations } from '@hooks'
import { theme } from '@theme'

interface ITextProps {
  translatedText: string
  style?: TextStyle
}

export const UiTitle = ({ translatedText, style }: ITextProps) => {
  const t = useTranslations()
  return (
    <Text
      style={[
        {
          marginTop: 20,
          fontSize: 20,
          lineHeight: 24,
          marginBottom: 40,
          textAlign: 'left',
          color: theme.colors.text
        },
        style,
      ]}
    >
      {t(translatedText)}
    </Text>
  )
}
