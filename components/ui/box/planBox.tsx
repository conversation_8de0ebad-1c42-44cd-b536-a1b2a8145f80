import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { theme } from '@theme'
import { useTranslations } from '@hooks'

const PlanBox = () => {
  const t = useTranslations()
  
  return (
    <View style={styles.container}>
      <View style={styles.overlay}>
        <Text style={styles.label}>{t('planBox.title')}</Text>
        <View style={styles.row}>
          <Text style={styles.price}>{t('planBox.typeBasic')}</Text>
          <Text style={styles.price}>
            15 PLN
            <Text style={styles.period}>{t('planBox.monthly')}</Text>
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 16,
    backgroundColor: theme.colors.lilyWhite,
    padding: 16,
    justifyContent: 'space-between',
    height: 100,
    borderWidth: 1,
    borderColor: '#DDD',
  },
  overlay: {
    flex: 1,
    justifyContent: 'space-between',
  },
  label: {
    color: theme.colors.gray,
    fontSize: 12,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  plan: {
    color: '#000',
    fontSize: 20,
    fontWeight: 'bold',
  },
  price: {
    fontSize: 16,
    fontWeight: 500,
  },
  period: {
    fontSize: 12,
    color: theme.colors.gray,
  },
});

export default PlanBox;
