import { FC } from 'react'
import Animated, {
  FadeInDown,
  FadeInUp,
  FadeOut,
  FadeOutUp,
} from 'react-native-reanimated'
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native'
import { theme } from '@theme'
import { Icon } from 'react-native-paper'
import { useTranslations } from '@hooks'

export enum NotificationType {
  Info = 'Info',
  Error = 'Error',
  Success = 'Success',
}

type TNotificationContainer = {
  type?: NotificationType
  label?: string
  icon?: string
  show?: boolean
  onPress: () => void
  translate?: boolean
}

const getIcon = (type: NotificationType) => {
  switch (type) {
    case 'Info':
      return 'information'
    case 'Error':
      return 'alert-outline'
    case 'Success':
    default:
      return 'check-outline'
  }
}

export const NotificationContainer: FC<TNotificationContainer> = ({
  type = NotificationType.Info,
  label,
  icon,
  show = false,
  translate,
  onPress,
}) => {
  const t = useTranslations()

  if (!show) {
    return null
  }

  return (
    <Animated.View
      style={[styles.container]}
      exiting={FadeOut}
      entering={FadeInDown}
    >
      <TouchableOpacity onPress={onPress} style={styles.touchableOpacity}>
        <Animated.View style={[styles.labelContainer]}>
          <View style={styles.iconContainer}>
            <Icon
              size={20}
              source={icon ? icon : getIcon(type)}
              color={theme.colors.error}
            />
          </View>
          <View>
            <Text style={[styles.textError]}>{label && translate  ? t(label) : label}</Text>
          </View>
        </Animated.View>
      </TouchableOpacity>
    </Animated.View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginBottom: 8,
  },
  touchableOpacity: {
    width: '100%',
  },
  labelContainer: {
    borderRadius: 8,
    backgroundColor: theme.colors.errorContainer,
    padding: 8,
    borderStyle: 'solid',
    borderColor: theme.colors.error,
    borderWidth: 1,
    flexDirection: 'row',
  },
  iconContainer: {
    marginRight: 8,
  },
  textError: {
    color: theme.colors.error,
  },
})
