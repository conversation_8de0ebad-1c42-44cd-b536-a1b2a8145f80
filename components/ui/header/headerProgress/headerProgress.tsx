import { View, StyleSheet } from 'react-native'
import { ProgressBar } from 'react-native-paper'

interface HeaderProgressProps {
  progress: number
  color: string
}

export const HeaderProgress = ({ progress, color }: HeaderProgressProps) => (
  <View style={styles.container}>
    <ProgressBar progress={progress} color={color} style={styles.bar} />
  </View>
)

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 20,
    justifyContent: 'center',
  },
  bar: {
    height: 6,
    borderRadius: 3,
  },
})
