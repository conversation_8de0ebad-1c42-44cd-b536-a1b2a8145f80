import { View, Text, ScrollView, NativeUIEvent, GestureResponderEvent, Keyboard } from 'react-native'
import { useTranslations } from '@hooks'
import { TextInputProps, TextInput } from 'react-native-paper'
import { theme } from '@theme'
import { forwardRef, useCallback, useState } from 'react'
import Animated, { FadeInUp, FadeOut } from 'react-native-reanimated'

interface TTextInputField extends Partial<TextInputProps> {
  value?: string
  labelTranslation: string
  errorMessage?: string
  isPasswordField?: boolean
}

export const TextInputField = forwardRef<TextInputProps, TTextInputField>(
  (
    {
      value,
      labelTranslation,
      errorMessage,
      onChangeText,
      isPasswordField = false,
      ...props
    },
    ref,
  ) => {
    const t = useTranslations()
    const [secureTextEntry, setSecureTextEntry] = useState(isPasswordField)

    const onIconEyePress = useCallback((e:  GestureResponderEvent) => {
      e.stopPropagation();
      Keyboard.dismiss();
      setSecureTextEntry(value => {
        return !value
      })
    }, [])

    return (
      <View>
        {labelTranslation && (
          <View
            style={{
              marginBottom: 10,
              height: 17,
            }}
          >
            <Text
              style={{
                fontSize: 14,
                lineHeight: 14,
                color: theme.colors.text,
              }}
            >
              {t(labelTranslation)}
            </Text>
          </View>
        )}
        <TextInput
          ref={ref}
          style={{
            height: 58,
            fontSize: 18,
          }}
          contentStyle={{
            paddingVertical: 20,
            paddingHorizontal: 16,
          }}
          outlineStyle={{
            borderRadius: 16,
          }}
          value={value}
          mode="outlined"
          outlineColor={theme.colors.inputDefaultBorderColor}
          onChangeText={onChangeText}
          error={!!errorMessage}
          right={
            isPasswordField ? (
              <TextInput.Icon
                icon={secureTextEntry ? 'eye' : 'eye-off'}
                onPress={onIconEyePress}
              />
            ) : null
          }
          secureTextEntry={secureTextEntry}
          {...props}
        />
        <View
          style={{
            marginTop: 8,
            height: 17,
            marginBottom: 2,
            overflow: 'hidden',
          }}
        >
          {errorMessage && (
            <Animated.View
              entering={FadeInUp.delay(400).duration(250)}
              exiting={FadeOut}
            >
              <Text
                style={{
                  color: theme.colors.error,
                  fontSize: 14,
                  lineHeight: 17,
                }}
              >
                {errorMessage}
              </Text>
            </Animated.View>
          )}
        </View>
      </View>
    )
  },
)
