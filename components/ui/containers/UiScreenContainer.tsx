import Animated, { FadeOut, SlideInRight } from 'react-native-reanimated'
import React, { useCallback } from 'react'
import {
  ViewStyle,
  StyleSheet,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native'
import { theme } from '@theme'
import type { EntryExitAnimationFunction } from 'react-native-reanimated/src/layoutReanimation/animationBuilder/commonTypes'
import type { BaseAnimationBuilder } from 'react-native-reanimated/src/layoutReanimation/animationBuilder/BaseAnimationBuilder'
import type { ReanimatedKeyframe } from 'react-native-reanimated/src/layoutReanimation/animationBuilder/Keyframe'

interface IUiScreenContainer {
  children: React.ReactNode
  styles?: ViewStyle
  entering?:
    | BaseAnimationBuilder
    | typeof BaseAnimationBuilder
    | EntryExitAnimationFunction
    | ReanimatedKeyframe
}

export const UiScreenContainer = ({
  children,
  styles,
  entering,
}: IUiScreenContainer) => {
  const onPress = useCallback(() => {
    Keyboard.dismiss()
  }, [])

  return (
    <KeyboardAvoidingView
      style={styleComponent.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <TouchableWithoutFeedback onPress={onPress}>
        <Animated.View
          entering={entering ?? SlideInRight}
          exiting={FadeOut}
          style={[styleComponent.containerChildren, styles]}
        >
          {children}
        </Animated.View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  )
}

const styleComponent = StyleSheet.create({
  container: {
    flex: 1,
  },
  containerChildren: {
    flex: 1,
    justifyContent: 'flex-start',
    alignContent: 'flex-start',
    backgroundColor: theme.colors.background,
    paddingHorizontal: 16,
  },
})
