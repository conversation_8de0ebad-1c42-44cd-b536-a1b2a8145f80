import { Portal } from 'react-native-paper'
import { Keyboard, StyleSheet, View, Modal } from 'react-native'
import { theme } from '@theme'
import { useEffect } from 'react'
import { useObserverState } from '@hooks'
import { IModal, notificationService } from '@services'
import { UiHeader } from '@ui/typography'
import { Image } from 'expo-image'
import { BlurView } from 'expo-blur'

const GIF_DIMENSION = 90

export const UiModal = () => {
  const { visible, message } = useObserverState<IModal>(
      notificationService.modalBehavior.getValue(),
      notificationService.modalObserver
  )

  useEffect(() => {
    Keyboard.dismiss()
  }, [])

  return (
      <Portal>
        <Modal visible={visible} transparent animationType="fade">
          <BlurView intensity={50} tint="light" style={StyleSheet.absoluteFill}>
            <View style={styles.modalContent}>
              <Image
                  source={require('../../../assets/reti-rotating.gif')}
                  style={styles.gif}
                  contentFit="contain"
                  priority="high"
              />
              {message && (
                  <UiHeader
                      translatedText={message ?? ''}
                      style={{ color: theme.colors.primary }}
                  />
              )}
            </View>
          </BlurView>
        </Modal>
      </Portal>
  )
}

const styles = StyleSheet.create({
  modalContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  gif: {
    width: GIF_DIMENSION,
    height: GIF_DIMENSION,
  },
})
