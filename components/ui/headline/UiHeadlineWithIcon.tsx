import React from 'react'
import { StyleSheet, View } from 'react-native'
import { Icon, Text } from 'react-native-paper'
import { useTranslations } from '@hooks'

interface UiHeadlineWithIconProps {
  icon: string
  headline: string
  textColor: string
}

const UiHeadlineWithIcon = ({
  icon,
  headline,
  textColor,
}: UiHeadlineWithIconProps) => {
  const t = useTranslations()

  return (
    <View style={styles.container}>
      <Icon source={icon} size={20} color={textColor} />
      <Text style={[styles.headline, { color: textColor }]}>{t(headline)}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingRight: 10,
  },
  headline: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: 600,
  },
})

export default UiHeadlineWithIcon
