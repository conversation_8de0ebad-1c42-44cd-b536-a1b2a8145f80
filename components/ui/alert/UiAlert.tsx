import * as React from 'react'
import { StyleSheet } from 'react-native'
import { Card, Icon, Text } from 'react-native-paper'
import { AlertStyles } from '@theme'
import { useTranslations } from '@hooks'

type AlertType = 'error' | 'warning' | 'info' | 'success'

interface UiAlertProps {
  message: string
  type: AlertType
}

export const UiAlert = ({ message, type }: UiAlertProps) => {
  const t = useTranslations()
  const { bgColor, borderColor, textColor, icon } = AlertStyles[type]

  return (
    <Card
      mode="outlined"
      style={{
        backgroundColor: bgColor,
        borderColor: borderColor,
        borderRadius: 8,
      }}
    >
      <Card.Content style={styles.cardContent}>
        <Icon source={icon} size={20} color={textColor} />
        <Text style={[styles.text, { color: textColor }]}>{t(message)}</Text>
      </Card.Content>
    </Card>
  )
}

const styles = StyleSheet.create({
  cardContent: {
    flexDirection: 'row',
    paddingVertical: 10,
    paddingHorizontal: 10,
  },
  text: {
    flexShrink: 1,
    marginLeft: 10,
    fontSize: 14,
  },
})
