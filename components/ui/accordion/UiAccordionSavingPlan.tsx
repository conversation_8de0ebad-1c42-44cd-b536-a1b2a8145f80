import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { Chip } from 'react-native-paper';
import { useTranslations } from '@hooks';
import { theme } from '@theme';
import { UiAlert } from '@ui/index';
import { UiAccordion } from '@ui/index'
type AlertType = 'success' | 'warning' | 'error' | 'info';

interface UiAccordionProps {
  id: string;
  icon: React.ReactNode;
  titleKey: string;
  descriptionKey: string;
  tagsKey?: string;
  alert?: {
    message: string;
    type: AlertType;
  };
  isSelected: boolean;
  onSelect: (id: string) => void;
  isTabSectionVisible: boolean;
  isAlertVisible: boolean;
}

export const UiAccordionSavingPlan = ({
  id,
  icon,
  titleKey,
  descriptionKey,
  tagsKey,
  alert,
  isSelected,
  onSelect,
  isTabSectionVisible = true,
  isAlertVisible = true,
}: UiAccordionProps) => {
  const t = useTranslations();
  const tags = tagsKey ? ((t as any)(tagsKey, { returnObjects: true }) as string[]) : [];

  return (
    <UiAccordion
      id={id}
      icon={icon}
      titleKey={titleKey}
      isSelected={isSelected}
      onSelect={onSelect}
      withAnimation={true}
    >
      <>
        <Text style={styles.description}>{t(descriptionKey)}</Text>
        {isTabSectionVisible && tags.length > 0 && (
          <View style={styles.tagsContainer}>
            {tags.map(tag => (
              <Chip
                key={tag}
                style={[
                  styles.chip,
                  id === 'adjusted' ? styles.chipAdjusted : styles.chipSimple,
                ]}
                textStyle={[
                  styles.chipText,
                  id === 'adjusted' ? styles.chipTextAdjusted : styles.chipTextSimple,
                ]}
              >
                {tag}
              </Chip>
            ))}
          </View>
        )}
        {isAlertVisible && alert && (
          <UiAlert message={alert.message} type={alert.type} />
        )}
      </>
    </UiAccordion>
  );
};

const styles = StyleSheet.create({
  description: {
    marginTop: 16,
    fontSize: 14,
    color: theme.colors.text,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginVertical: 12,
    gap: 4,
  },
  chip: {
    borderRadius: 100,
    paddingHorizontal: 6,
    paddingVertical: 3,
    marginBottom: 6,
    borderWidth: 1,
  },
  chipText: {
    fontWeight: '400',
    fontSize: 12,
  },
  chipAdjusted: {
    backgroundColor: theme.colors.mintGreen,
    borderColor: theme.colors.magicMint,
  },
  chipTextAdjusted: {
    color: theme.colors.shamrock,
  },
  chipSimple: {
    backgroundColor: theme.colors.lilyWhite,
    borderColor: theme.colors.inputDefaultBorderColor,
  },
  chipTextSimple: {
    color: theme.colors.eclipse,
  },
});