import { View, Text, StyleSheet } from 'react-native'
import { useTranslations } from '@hooks'
import { theme } from '@theme'

type Props = {
  label?: string
}

const UiDividerWithLabel = ({ label }: Props) => {
  const t = useTranslations()
  return (
    <View style={styles.container}>
      <View style={styles.divider} />
      <View style={styles.label}>
        {label && <Text style={styles.labelText}>{t(label)}</Text>}
      </View>
      <View style={styles.divider} />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    marginHorizontal: 10,
  },
  labelText: {
    color: theme.colors.gray,
  },
  divider: {
    height: 1,
    backgroundColor: theme.colors.inputDefaultBorderColor,
    flex: 3,
  },
})

export default UiDividerWithLabel
