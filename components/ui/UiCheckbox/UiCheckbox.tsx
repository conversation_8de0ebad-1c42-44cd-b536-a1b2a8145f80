import { forwardRef, useCallback, useEffect, useState } from 'react'
import { Icon, Text } from 'react-native-paper'
import { StyleSheet, TouchableOpacity, View } from 'react-native'
import { theme } from '@theme'

interface Props {
  label: string
  initialChecked: boolean
  onPress?: (v: boolean) => void
  required?:boolean
}

export const UiCheckbox = forwardRef(
  ({ label, initialChecked = false, onPress, required }: Props, ref) => {
    const [checked, setChecked] = useState(initialChecked)

    useEffect(() => {
      setChecked(initialChecked)
    }, [initialChecked])

    const onCheckboxPress = useCallback(() => {
      setChecked(v => {
        onPress?.(!v)
        return !v
      })
    }, [])

    return (
      <TouchableOpacity style={styles.checkBoxContainer} onPress={onCheckboxPress}>
        <View style={styles.checkBox}>
          <Icon
            source={checked?'checkbox-outline':'checkbox-blank-outline'}
            size={28}
            color={theme.colors.gray}
          />
        </View>
        <Text style={styles.text}>{label}</Text>
        {required && (
          <Text style={[styles.text, {color: theme.colors.error}]}>{' *'}</Text>
        )}
      </TouchableOpacity>
    )
  },
)

const styles = StyleSheet.create({
  checkBoxContainer:{
    flexDirection: 'row',
    alignItems:'center',
    paddingVertical:10
  },
  checkBox: {
    alignItems: 'center',
    justifyContent: 'flex-start',
    margin:2,
    marginRight:10,
  },
  checkBoxLabel: {
    color: theme.colors.gray,
    justifyContent: 'flex-start',
  },
  text:{
    fontSize:16,
    lineHeight:24
  }
})
