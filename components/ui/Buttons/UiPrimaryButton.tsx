import { But<PERSON>, <PERSON><PERSON> } from 'react-native-paper'
import React from 'react'
import { useTranslations } from '@hooks'
import { TextStyle, View, ViewStyle } from 'react-native'

type TProps = Omit<Props, 'label' | 'icon' | 'extended'> & {
  translationTitle: string
  buttonStyle?: ViewStyle
  containerStyle?: ViewStyle
  contentStyle?: ViewStyle
  labelStyle?: TextStyle
  mode: 'contained' | 'outlined'
  loading?: boolean
}

export const UiPrimaryButton = ({
  onPress,
  translationTitle,
  buttonStyle,
  containerStyle,
  contentStyle,
  labelStyle,
  mode = 'contained',
  ...rest
}: TProps) => {
  const t = useTranslations()
  return (
    <View style={[{ width: '100%' }, containerStyle]}>
      <Button
        mode={mode}
        contentStyle={[
          {
            height: 58,
          },
          contentStyle,
        ]}
        style={[
          {
            borderRadius: 35,
          },
          buttonStyle,
        ]}
        labelStyle={[
          {
            fontSize: 18,
          },
          labelStyle,
        ]}
        onPress={onPress}
        {...rest}
      >
        {t(translationTitle)}
      </Button>
    </View>
  )
}
