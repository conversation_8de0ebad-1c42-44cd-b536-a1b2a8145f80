import { IconButton } from 'react-native-paper'
import { theme } from '@theme'
import { useRouter } from 'expo-router'
import { useCallback } from 'react'

const UiHeaderBackButton = ({overrideOnPress} :{overrideOnPress?: ()=> void}) => {
  const router = useRouter()

  const onPress = useCallback(() => {
    router.back()
  }, [router])

  return <IconButton icon="arrow-left" size={18} onPress={overrideOnPress ?? onPress} containerColor={theme.headerLeft.background} style={{marginLeft: -4}}/>
}

export default UiHeaderBackButton
