import React, { useCallback } from 'react'
import { useRouter } from 'expo-router'
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'
import { ViewStyle, TextStyle } from 'react-native'

type Props = {
  route: string
  translationTitle: string
  mode?: 'outlined' | 'contained'
  containerStyle?: ViewStyle
  buttonStyle?: ViewStyle
  contentStyle?: ViewStyle
  labelStyle?: TextStyle
}

const UIPrimaryRouterButton: React.FC<Props> = ({
  route,
  translationTitle,
  mode = 'contained',
  containerStyle,
  buttonStyle,
  contentStyle,
  labelStyle,
}) => {
  const router = useRouter()

  const onPress = useCallback(() => {
    router.push(route)
  }, [route, router])

  return (
    <UiPrimaryButton
      translationTitle={translationTitle}
      mode={mode}
      onPress={onPress}
      containerStyle={containerStyle}
      buttonStyle={buttonStyle}
      contentStyle={contentStyle}
      labelStyle={labelStyle}
    />
  )
}

export default UIPrimaryRouterButton
