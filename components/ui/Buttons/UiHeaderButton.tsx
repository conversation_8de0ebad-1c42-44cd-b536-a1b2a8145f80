import React from 'react';
import { StyleSheet, Text, TouchableOpacity, ViewStyle, TextStyle } from 'react-native';
import { useTranslations } from '@hooks';

type UiHeaderButtonProps = {
  onPress: () => void;
  translationTitle: string;
  style?: ViewStyle;
  textStyle?: TextStyle;
  testID?: string;
};

export const UiHeaderButton: React.FC<UiHeaderButtonProps> = ({
  onPress,
  translationTitle,
  style,
  textStyle,
  testID,
}) => {
  const t = useTranslations();
  
  return (
    <TouchableOpacity 
      style={[styles.button, style]} 
      onPress={onPress}
      activeOpacity={0.7}
      testID={testID}
    >
      <Text style={[styles.buttonText, textStyle]}>
        {t(translationTitle)}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#00C2A8',
    borderRadius: 100,
    paddingHorizontal: 16,
    paddingVertical: 6,
    justifyContent: 'center',
    alignItems: 'center',
    height: 42,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
    includeFontPadding: false,
  },
});