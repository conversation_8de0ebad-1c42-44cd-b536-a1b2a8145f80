import { useState, useCallback } from 'react'
import { IconButton } from 'react-native-paper'
import { useRouter } from 'expo-router'
import { theme } from '@theme'
import ConfirmExitDialog from '@ui/dialog/ConfirmExitDialog'
import { useTranslations } from '@hooks'

const UiHeaderCloseButton = () => {
  const [visible, setVisible] = useState(false)
  const router = useRouter()
  const t = useTranslations()
  const showDialog = useCallback(() => {
    setVisible(true)
  }, [])

  const hideDialog = useCallback(() => {
    setVisible(false)
  }, [])

  const handleConfirm = useCallback(() => {
    hideDialog()
    router.replace({ pathname: '(authenticated)' }) 
  }, [router, hideDialog])

  return (
    <>
      <IconButton
        icon="close"
        size={22}
        onPress={showDialog}
        containerColor={theme.headerLeft.background}
        style={{ marginRight: 0 }}
      />
      <ConfirmExitDialog
        visible={visible}
        onConfirm={hideDialog}
        onDismiss={handleConfirm}
        title={t('exitDialog.title')}
        description={t('exitDialog.description')}
        cancelLabel={('common.yes')}
        confirmLabel={('common.no')}
      />
    </>
  )
}

export default UiHeaderCloseButton
