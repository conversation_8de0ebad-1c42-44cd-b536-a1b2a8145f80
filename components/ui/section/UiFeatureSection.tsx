import React from 'react'
import { StyleSheet, View } from 'react-native'
import { theme } from '@theme'
import UiHeadlineWithIcon from '@ui/headline/UiHeadlineWithIcon'
import { UiTitle } from '@ui/typography/UiTitle'

interface UiFeatureSectionProps {
  icon: string
  headline: string
  translatedText: string
}

const UiFeatureSection = ({
  icon,
  headline,
  translatedText,
}: UiFeatureSectionProps) => (
  <View>
    <UiHeadlineWithIcon
      icon={icon}
      headline={headline}
      textColor={theme.colors.primary}
    />
    <UiTitle translatedText={translatedText} style={styles.title} />
  </View>
)

export default UiFeatureSection

const styles = StyleSheet.create({
  title: {
    marginTop: 0,
    fontSize: 16,
    marginBottom: 0,
    marginLeft: 30,
    fontWeight: 400,
    paddingRight: 10,
    color: theme.colors.mineralGreen,
  },
})
