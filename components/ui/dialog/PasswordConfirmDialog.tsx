import React, { ReactNode, useState, useRef } from 'react';
import { useTranslations } from '@hooks';
import BaseModal from '@ui/dialog/BaseModal';
import { StyleSheet } from 'react-native';
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton';
import { TextInputField } from '@ui/TextInputField/TextInputField';

interface PasswordConfirmDialogProps {
  visible: boolean;
  onConfirm: (password: string) => void;
  onDismiss: () => void;
  title?: string;
  description?: string;
}

const PasswordConfirmDialog = ({
  visible,
  onConfirm,
  onDismiss,
  title,
  description,
}: PasswordConfirmDialogProps) => {
  const t = useTranslations();
  const [password, setPassword] = useState('');
  const passwordRef = useRef<any>(null);

  const handleConfirm = () => {
    onConfirm(password);
    setPassword('');
  };

  return (
    <BaseModal
      visible={visible}
      onDismiss={onDismiss}
      animationType="fade"
      showLogo
      title={title || t('viewKeyModal.title')}
      description={description || t('viewKeyModal.description')}
    >
      <TextInputField
        ref={passwordRef}
        labelTranslation="signIn.inputs.password.label"
        value={password}
        onChangeText={setPassword}
        isPasswordField
      />

      <UiPrimaryButton
        translationTitle="common.show"
        onPress={handleConfirm}
        style={styles.confirmButton}
        mode="contained"
      />
    </BaseModal>
  );
};

const styles = StyleSheet.create({
  confirmButton: {
    marginTop: 24,
  },
});

export default PasswordConfirmDialog;