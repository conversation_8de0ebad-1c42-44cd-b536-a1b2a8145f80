import React from 'react';
import { Modal, View, StyleSheet, ViewStyle, Text } from 'react-native';
import { theme } from '@theme';
import SvgLogGreen from '../../../assets/logo-reti-green.svg';

interface BaseModalProps {
  visible: boolean;
  onDismiss: () => void;
  children: React.ReactNode;
  animationType?: 'none' | 'slide' | 'fade';
  modalStyle?: ViewStyle;
  contentStyle?: ViewStyle;
  showLogo?: boolean;
  title?: string;
  description?: string;
}

const BaseModal = ({
  visible,
  onDismiss,
  children,
  animationType = 'slide',
  modalStyle,
  contentStyle,
  showLogo = false,
  title,
  description,
}: BaseModalProps) => {
  return (
    <Modal
      transparent
      visible={visible}
      onRequestClose={onDismiss}
      animationType={animationType}
    >
      <View style={[styles.backdrop, modalStyle]}>
        <View style={[styles.dialog, contentStyle]}>
          {showLogo && (
            <View style={styles.iconContainer}>
              <SvgLogGreen width={58} height={58} />
            </View>
          )}

          {title && <Text style={styles.title}>{title}</Text>}
          {description && <Text style={styles.description}>{description}</Text>}

          {children}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  dialog: {
    backgroundColor: theme.colors.lilyWhite,
    padding: 24,
    borderTopRightRadius: 32,
    borderTopLeftRadius: 32,
    gap: 16,
    height: '60%',
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    textAlign: 'center',
    fontSize: 32,
    marginBottom: 16,
  },
  description: {
    textAlign: 'center',
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 24,
  },
});

export default BaseModal;