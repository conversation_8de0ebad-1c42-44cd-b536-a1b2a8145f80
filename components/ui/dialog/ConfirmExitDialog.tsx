import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useTranslations } from '@hooks';
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton';
import BaseModal from '@ui/dialog/BaseModal';
import { theme } from '@theme';

type ConfirmExitDialogProps = {
  visible: boolean;
  onConfirm: () => void;
  onDismiss: () => void;
  title?: string;
  description?: string;
  confirmLabel?: string;
  cancelLabel?: string;
};

const ConfirmExitDialog = ({
  visible,
  onConfirm,
  onDismiss,
  title,
  description,
  confirmLabel,
  cancelLabel,
}: ConfirmExitDialogProps) => {
  const t = useTranslations();

  return (
    <BaseModal
      visible={visible}
      onDismiss={onDismiss}
      showLogo
      title={title || t('exitDialog.title')}
      description={description || t('exitDialog.description')}
    >
      <View style={styles.actions}>
        <UiPrimaryButton
          translationTitle={cancelLabel || 'common.no'}
          onPress={onDismiss}
          mode="outlined"
          containerStyle={styles.buttonContainer}
          buttonStyle={styles.cancelButton}
          contentStyle={styles.buttonContent}
          labelStyle={styles.buttonLabel}
        />
        <UiPrimaryButton
          translationTitle={confirmLabel || 'common.yes'}
          onPress={onConfirm}
          mode="contained"
          containerStyle={styles.buttonContainer}
          buttonStyle={styles.confirmButton}
          contentStyle={styles.buttonContent}
          labelStyle={styles.buttonLabel}
        />
      </View>
    </BaseModal>
  );
};

const styles = StyleSheet.create({
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
    marginTop: 'auto',
  },
  buttonContainer: {
    flex: 1,
  },
  buttonContent: {
    height: 58,
  },
  buttonLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderColor: theme.colors.primary,
    borderWidth: 2,
  },
  confirmButton: {
    backgroundColor: theme.colors.primary,
  },
});

export default ConfirmExitDialog;