import React, { useState, useEffect } from 'react'
import { View, StyleSheet, TextInput } from 'react-native'
import { IconButton, Text } from 'react-native-paper'
import { useTranslations } from '@hooks'
import { theme } from '@theme'

type UiStepperProps = {
  value: number | null
  min: number
  max: number
  step: number
  unit: string
  placeholder: string
  onChange: (newValue: number | null) => void
}

export const UiStepper = ({
  value,
  min = 0,
  max = 9999999,
  step = 1,
  unit = '',
  placeholder = '0',
  onChange,
}: UiStepperProps) => {
  const t = useTranslations()
  const [inputValue, setInputValue] = useState(
    value !== undefined && value !== null ? value.toString() : placeholder,
  )

  useEffect(() => {
    if (value !== undefined && value !== null) {
      setInputValue(value.toString())
    }
  }, [value])

  const clamp = (val: number) => Math.max(min, Math.min(max, val))

  const currentValue = value ?? 0

  const handleDecrease = () => {
    onChange(clamp(currentValue - step))
  }

  const handleIncrease = () => {
    onChange(clamp(currentValue + step))
  }

  const handleInputChange = (text: string) => {
    const cleaned = text.replace(',', '.').replace(/[^0-9.]/g, '')
    const parts = cleaned.split('.')

    if (parts.length > 2) return

    if (parts[1]?.length > 2) return

    const numeric = parseFloat(cleaned)

    if (isNaN(numeric)) {
      setInputValue(cleaned)
      return
    }

    if (numeric > max || numeric < min) return

    onChange(numeric)
    setInputValue(cleaned)
  }

  const handleBlur = () => {
    const trimmed = inputValue.trim()
    const numeric = parseFloat(trimmed)

    if (trimmed === '' || isNaN(numeric)) {
      setInputValue(min.toString())
      onChange(min)
      return
    }

    if (trimmed.endsWith('.')) {
      setInputValue(numeric.toString())
      onChange(numeric)
    }
  }

  return (
    <View style={styles.container}>
      <IconButton
        icon="minus"
        mode="outlined"
        containerColor={theme.colors.onPrimary}
        iconColor={theme.colors.primary}
        size={24}
        style={styles.iconButton}
        onPress={handleDecrease}
        disabled={value !== null && value <= min}
      />
      <View style={styles.inputWrapper}>
        <TextInput
          style={styles.input}
          value={inputValue}
          onChangeText={handleInputChange}
          onBlur={handleBlur}
          keyboardType="decimal-pad"
          textAlign="center"
          placeholder={placeholder}
        />
        {unit ? <Text style={styles.unit}>{t(unit)}</Text> : null}
      </View>
      <IconButton
        icon="plus"
        mode="outlined"
        containerColor={theme.colors.onPrimary}
        iconColor={theme.colors.primary}
        size={24}
        style={styles.iconButton}
        onPress={handleIncrease}
        disabled={value !== null && value >= max}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    width: 58,
    height: 58,
    borderWidth: 2,
    borderRadius: 16,
    borderColor: theme.colors.primary,
  },
  inputWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 16,
    borderColor: theme.colors.inputDefaultBorderColor,
    position: 'relative',
    marginHorizontal: 4,
    height: 58,
  },
  input: {
    fontSize: 18,
    fontWeight: '400',
    color: theme.colors.black,
    paddingVertical: 0,
    paddingHorizontal: 0,
    width: '100%',
    textAlign: 'center',
  },
  unit: {
    position: 'absolute',
    right: 16,
    fontSize: 16,
    color: theme.colors.sageGreen,
  },
})
