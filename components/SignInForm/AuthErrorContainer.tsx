import {
  NotificationContainer,
  NotificationType,
} from '@ui/Notification/NotificationContainer'
import React, { useCallback, useState } from 'react'
import { useOnError } from '@hooks'
import { AppError, <PERSON>rror<PERSON>ey, errorHandlerService } from '@services'

interface Props {
  callback?: () => void,
  errorKey: <PERSON><PERSON><PERSON><PERSON><PERSON>
}

export const AuthErrorContainer = ({ callback, errorKey = ErrorKey.AuthError }: Props) => {
  const [show, setShow] = useState(false)
  const [errorMessage, setErrorMessage] = useState<AppError | undefined>(
    undefined,
  )
  useOnError(errorKey, error => {
    if (error) {
      callback?.()
      setShow(true)
      setErrorMessage(error)
    } else {
      setShow(false)
      setErrorMessage(undefined)
    }
  })

  const onPress = useCallback(() => {
    setShow(false)
    callback?.()
    errorHandlerService.clearError(errorKey)
  }, [])

  return (
    <NotificationContainer
      type={NotificationType.Error}
      label={errorMessage?.errorText}
      show={show}
      onPress={onPress}
    />
  )
}
