import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton';
import {
  authorizationService,
  errorHandlerService,
  ErrorKey,
  notificationService,
  registrationService,
  tokenService,
} from '@services';
import { TextInputField } from '@ui/TextInputField/TextInputField';
import { View, StyleSheet } from 'react-native';
import { useOnKeyboardHide, useToggle, useValidatorString } from '@hooks';
import { emailScheme, passwordSignInSchema } from '@utils';
import { AuthErrorContainer } from './AuthErrorContainer';
import { Link, useGlobalSearchParams, useRouter } from 'expo-router';
import { UiTitle } from '@ui/typography/UiTitle';
import { theme } from '@theme';

const SignInForm = () => {
  const emailRef = useRef<any>(null);
  const passwordRef = useRef<any>(null);
  const params = useGlobalSearchParams();
  const [email, setEmail] = useState(
    (params?.email as string) || params?.username || '',
  );
  const [emailValidatorMessage, validateEmail, isValidEmail] =
    useValidatorString(emailScheme);
  const [passwordValidatorMessage, validatePassword, isValidPassword] =
    useValidatorString(passwordSignInSchema);

  const [password, setPassword] = useState('');
  const [loading, setIsLoading] = useToggle(false);
  const router = useRouter();

  useEffect(() => {
    passwordRef.current?.focus();
  }, []);

  const onEmailFocus = useCallback(() => {
    clearAuthError();
  }, []);

  const onPasswordFieldFocus = useCallback(() => {
    clearAuthError();
  }, []);

  const handleEmailChange = (text: string) => {
    validateEmail(text);
    setEmail(text);
    clearAuthError();
  };

  const handlePasswordChange = (text: string) => {
    validatePassword(text);
    setPassword(text);
    clearAuthError();
  };

  const clearAuthError = useCallback(() => {
    errorHandlerService.clearError(ErrorKey.AuthError);
  }, []);

  const submitForm = useCallback(async () => {
    setIsLoading(true);
    authorizationService.signIn({ username: email, password }).subscribe({
      next: response => {
        console.log('response', response);
        authorizationService.authorize();
        tokenService.setToken(response);
        notificationService.hideModal();
      },
      error: error => {
        console.log(error);
        if (error.status === 401 && error?.response?.data?.error_id === '001') {
          registrationService
            .resendActivationCode(email as string)
            .subscribe(() => {
              setIsLoading(false);
              router.push({
                pathname: '/activation-code',
                params: { username: email },
              });
            });
        } else {
          if (error?.response?.data) {
            errorHandlerService.add(
              ErrorKey.AuthError,
              (Object.values(error?.response?.data)?.[0] as string) ||
                'unknown error',
              error?.status,
            );
          }
          setIsLoading(false);
        }

        notificationService.hideModal();
      },
    });
  }, [email, password]);

  useOnKeyboardHide(() => {
    emailRef.current?.blur();
    passwordRef.current?.blur();
    clearAuthError();
  });

  const isButtonEnabled = useMemo(
    () => isValidEmail(email) && isValidPassword(password),
    [email, password],
  );

  return (
    <View style={styles.container}>
      <View>
        <AuthErrorContainer
          callback={() => {
            setIsLoading(false);
          }}
        />
      </View>
      <View style={styles.inputContainer}>
        <TextInputField
          ref={emailRef}
          labelTranslation="signIn.inputs.email.label"
          value={email}
          onChangeText={handleEmailChange}
          keyboardType="email-address"
          autoComplete="email"
          autoCapitalize="none"
          errorMessage={emailValidatorMessage}
          onFocus={onEmailFocus}
        />
        <TextInputField
          ref={passwordRef}
          labelTranslation="signIn.inputs.password.label"
          value={password}
          onChangeText={handlePasswordChange}
          onFocus={onEmailFocus}
          errorMessage={passwordValidatorMessage}
          isPasswordField
        />
        <View style={styles.passwordRememberContainer}>
          <Link href={{ pathname: '/(password-reset)', params: { email } }}>
            <UiTitle
              translatedText="common.passwordForgotten"
              style={styles.link}
            />
          </Link>
        </View>
      </View>
      <View style={styles.buttonContainer}>
        <UiPrimaryButton
          onPress={submitForm}
          translationTitle="signIn.buttons.signIn.label"
          loading={loading}
          disabled={!isButtonEnabled}
          onFocus={onPasswordFieldFocus}
        />
      </View>
    </View>
  );
};

export default SignInForm;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-start',
  },
  inputContainer: {
    flex: 3,
  },
  buttonContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    marginBottom: 20,
  },
  passwordRememberContainer: {
    alignItems: 'flex-end',
    marginBottom: 20,
  },
  link: {
    color: theme.colors.primary,
    fontSize: 16,
    lineHeight: 18,
  },
});
