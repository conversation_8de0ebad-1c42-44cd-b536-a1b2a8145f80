import React, { useCallback, useMemo, useRef, useState } from 'react';
import style from './SignInForm.styles';
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton';
import { errorHandlerService, ErrorKey } from '@services';
import { TextInputField } from '@ui/TextInputField/TextInputField';
import { View } from 'react-native';
import { useOnKeyboardHide, useValidatorString } from '@hooks';
import { emailScheme } from '@utils';
import { useRouter } from 'expo-router';

type IEmailForm = {
  redirectPath: string;
};

const EmailForm = ({ redirectPath }: IEmailForm) => {
  const emailRef = useRef<any>(null);
  const passwordRef = useRef<any>(null);
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [emailValidatorMessage, validateEmail, isValidEmail] =
    useValidatorString(emailScheme);

  const handleEmailChange = (text: string) => {
    validateEmail(text);
    setEmail(text);
    clearAuthError();
  };

  const clearAuthError = useCallback(() => {
    errorHandlerService.clearAllErrors();
  }, []);

  const submitForm = useCallback(async () => {
    clearAuthError();
    router.push({
      pathname: redirectPath,
      params: { email },
    });
  }, [email]);

  useOnKeyboardHide(() => {
    emailRef.current?.blur();
    passwordRef.current?.blur();
    clearAuthError();
  });

  const isButtonEnabled = useMemo(() => isValidEmail(email), [email]);

  return (
    <View
      style={{
        justifyContent: 'flex-start',
      }}
    >
      <TextInputField
        ref={emailRef}
        labelTranslation="signIn.inputs.email.label"
        value={email}
        onChangeText={handleEmailChange}
        keyboardType="email-address"
        autoComplete="email"
        autoCapitalize="none"
        errorMessage={emailValidatorMessage}
      />
      <UiPrimaryButton
        mode={!isButtonEnabled ? 'outlined' : 'contained'}
        onPress={submitForm}
        translationTitle="common.continue"
        disabled={!isButtonEnabled}
        containerStyle={style.mainActionButtonContainer}
      />
    </View>
  );
};

export default EmailForm;
