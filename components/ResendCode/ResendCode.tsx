import { Button, Text } from 'react-native-paper'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { errorHandlerService, ErrorKey, registrationService } from '@services'
import { useRouter } from 'expo-router'
import { View } from 'react-native'
import { theme } from '@theme'

const COUNTER_VALUE_MS = 10000

export default ({
  username,
  onResend,
}: {
  username: string
  onResend?: () => void
}) => {
  const [isBlocked, setIsBlocked] = useState(false)
  const [counterTime, setCounterTime] = useState(COUNTER_VALUE_MS)
  const intervalRef = useRef(null)
  const router = useRouter()

  useEffect(() => {
    if (counterTime === 0 && intervalRef.current) {
      clearInterval(intervalRef.current)
      setIsBlocked(false)
      setCounterTime(COUNTER_VALUE_MS)
    }
  }, [counterTime])

  const startCounter = useCallback(() => {
    setIsBlocked(true)
    intervalRef.current = setInterval(() => {
      setCounterTime(value => value - 1000)
    }, 1000)
  }, [])

  const resendCode = useCallback(() => {
    startCounter()
    registrationService.resendActivationCode(username).subscribe({
      next: () => {
        router.replace({ pathname: '(confirmation)', params: { username } })
      },
      error: error => {
        errorHandlerService.add(
          ErrorKey.RegistrationError,
          Object.values(error.response.data)?.[0] as string,
        )
      },
    })
  }, [router, startCounter])

  return (
    <View>
      {isBlocked && (
        <View>
          <Text variant="bodyLarge">{`Kod nie dotarł? Wyślij ponownie za  ${Math.round(counterTime / 1000)} sekund`}</Text>
        </View>
      )}
      {!isBlocked && (
        <Button mode="text" onPress={resendCode}>
          <Text variant="bodyLarge" style={{color: theme.colors.primary}}>{'Kod nie dotarł? Wyślij ponownie'}</Text>
        </Button>
      )}
    </View>
  )
}
