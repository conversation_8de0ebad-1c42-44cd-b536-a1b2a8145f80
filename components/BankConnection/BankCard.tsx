import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ImageSourcePropType,
  TouchableOpacity
} from 'react-native';
import PasswordConfirmDialog from '@ui/dialog/PasswordConfirmDialog';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface BankCardProps {
  logo: ImageSourcePropType;
  name: string;
  accountNumber: string;
  showMask?: boolean;
  children?: React.ReactNode;
  allowUnmask?: boolean;
}

export const BankCard: React.FC<BankCardProps> = ({
  logo,
  name,
  accountNumber,
  showMask = true,
  children,
  allowUnmask = false,
}) => {
  const [isMasked, setIsMasked] = useState(showMask);
  const [showPasswordModal, setShowPasswordModal] = useState(false);

  const toggleMask = () => {
    if (!allowUnmask) return;
    
    if (isMasked) {
      setShowPasswordModal(true);
    } else {
      setIsMasked(true);
    }
  };

  const handlePasswordCorrect = (password: string) => {
    if (password === 'haslo123') {
      setIsMasked(false);
    }
    setShowPasswordModal(false);
  };

  const getMaskedAccount = (acc: string) => {
    if (!acc.startsWith('PL')) return acc;
    const visible = acc.slice(-4);
    return `PL  ••••  ••••  ••••  ••••  ${visible}`;
  };

  return (
    <View style={styles.card}>
      <View style={styles.topRow}>
        <Image source={logo} style={styles.logo} />
        <Text style={styles.name}>{name}</Text>
      </View>

      <View style={styles.accountRow}>
        <Text style={styles.accountNumber}>
          {isMasked ? getMaskedAccount(accountNumber) : accountNumber}
        </Text>
        {allowUnmask && (
          <TouchableOpacity onPress={toggleMask}>
            <Icon 
              name={isMasked ? 'visibility-off' : 'visibility'} 
              size={24} 
              color="#3F3F3F" 
            />
          </TouchableOpacity>
        )}
      </View>

      {children && <View style={styles.children}>{children}</View>}

      <PasswordConfirmDialog
        visible={showPasswordModal}
        onConfirm={handlePasswordCorrect}
        onDismiss={() => setShowPasswordModal(false)}
      />
    </View>
  );
};
const styles = StyleSheet.create({
  card: {
    width: '100%',
    backgroundColor: '#F9FAFA',
    borderRadius: 24,
    borderWidth: 1,
    borderColor: '#DEE3E1',
    padding: 16,
    gap: 16
  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12
  },
  logo: {
    width: 32,
    height: 32
  },
  name: {
    fontSize: 16,
    color: '#3F3F3F',
    fontWeight: '400',
    lineHeight: 24
  },
  accountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  accountNumber: {
    fontSize: 18,
    color: '#131615',
  },
  eye: {
    fontSize: 18
  },
  children: {
    marginTop: 8
  }
})