import React, { useState } from 'react'
import { View, StyleSheet } from 'react-native'
import { UiHeaderButton } from '@ui/Buttons/UiHeaderButton'
import ConfirmExitDialog from '@ui/dialog/ConfirmExitDialog'
import { useTranslations } from '@hooks'

interface BankActionButtonsProps {
  onLeftButtonClick: () => void
  onRightButtonClick: () => void
  leftButtonText?: string
  rightButtonText?: string
}

export const BankActionButtons: React.FC<BankActionButtonsProps> = ({
  onLeftButtonClick,
  onRightButtonClick,
  leftButtonText = 'bank-connection.disconect',
  rightButtonText = 'bank-connection.renew'
}) => {
  const t = useTranslations()
  const [showConfirm, setShowConfirm] = useState(false)

  const handleConfirm = () => {
    onLeftButtonClick()
    setShowConfirm(false)
  }

  const handleCancel = () => {
    setShowConfirm(false)
  }

  return (
    <>
      <View style={styles.buttonRow}>
        <UiHeaderButton
          translationTitle={leftButtonText}
          onPress={() => setShowConfirm(true)}
          style={styles.disconnectButton}
          textStyle={styles.disconnectButtonText}
        />

        <UiHeaderButton
          translationTitle={rightButtonText}
          onPress={onRightButtonClick}
          style={styles.rightButton}
          textStyle={styles.rightButtonText}
        />
      </View>

    <ConfirmExitDialog
      visible={showConfirm}
      onConfirm={handleConfirm}
      onDismiss={handleCancel}
      title={t('bank-connection.discconectModal.title')}
      description={t('bank-connection.discconectModal.description')}
      confirmLabel={'bank-connection.discconectModal.confirm'}
      cancelLabel={'bank-connection.discconectModal.cancel'}
    />
    </>
  )
}
const styles = StyleSheet.create({
  buttonRow: {
    flexDirection: 'row',
    gap: 16,
    marginVertical: 16
  },
  disconnectButton: {
    flex: 1,
    height: 42,
    backgroundColor: 'white',
    borderColor: '#2A322F',
    borderWidth: 1
  },
  disconnectButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2A322F'
  },
  rightButton: {
    flex: 1,
    height: 42,
    backgroundColor: '#2A322F'
  },
  rightButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white'
  }
})