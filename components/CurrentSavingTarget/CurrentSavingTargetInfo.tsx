import { Card } from 'react-native-paper'
import useSavingPlanStore from '@store/saving-plan-store'
import {  InfoSavedPlanContainer } from '../../app/(saving-plan)/summary'
import { StyleSheet, View } from 'react-native'
import {notificationService} from "@services";
import {useEffect} from "react";

export const CurrentSavingTargetInfo = ()=> {

  const {
    planName,
    selectedTarget,
  } = useSavingPlanStore(state => ({
    planName: state.planName,
    selectedTarget: state.selectedTarget,
  }))

  return <Card>
    <Card.Title
      title={`${planName}`}
      subtitle={selectedTarget?.icon_label}
      titleStyle={{fontSize:26, marginTop:30}}
      subtitleStyle={{fontSize:22, marginVertical:10}}
    />
    <View style={styles.container}>
      <InfoSavedPlanContainer/>
    </View>
  </Card>
}

const styles = StyleSheet.create({
  container: {
    padding: 30
  }
})
