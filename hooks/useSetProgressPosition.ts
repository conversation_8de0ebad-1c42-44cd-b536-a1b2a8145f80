import { useCallback } from 'react'
import { useFocusEffect } from 'expo-router'
import useProgressStore from '@store/progress.store'

const useSetProgressPosition = (currentStep: number, totalSteps: number) => {
  const setProgress = useProgressStore(state => state.setProgress)

  useFocusEffect(
    useCallback(() => {
      const progress = currentStep / totalSteps
      setProgress(progress)
    }, [currentStep, totalSteps, setProgress]),
  )
}

export default useSetProgressPosition
