import { useObserverCallback } from './index'
import { authorizationService, notificationService } from '@services'
import { onAuthPending } from '@operators/onAuthPending'

const useOnAuthorizationPending = (isLoaded: boolean): any => {
  useObserverCallback<boolean>(
    authorizationService.getObserver().pipe(onAuthPending()),
    () => {
      notificationService.showModal('messages.loading')
    },
  )
}

export default useOnAuthorizationPending
