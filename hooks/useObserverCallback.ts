import { Observable, Subscription } from 'rxjs'
import { useEffect, useRef } from 'react'

const useObserverCallback = <T>(
  observer: Observable<T>,
  callback: (props: T) => void,
) => {
  const subscriptionRef = useRef<Subscription | null>(null)

  useEffect(() => {
    subscriptionRef.current = observer.subscribe(callback)
    return () => {
      subscriptionRef.current?.unsubscribe()
    }
  }, [])
}

export default useObserverCallback
