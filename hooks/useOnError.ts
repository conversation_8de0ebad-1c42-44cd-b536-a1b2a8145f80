import { useObserverCallback } from './index'
import { errorHandlerService, AppError, Error<PERSON>ey } from '@services'

const observable = errorHandlerService.getObserver()

const useOnError = (
  errorKey: <PERSON><PERSON><PERSON><PERSON><PERSON>,
  callback?: (error: AppError | undefined) => void,
) => {
  useObserverCallback(observable, errors => {
    const error = errors.find(({ key }) => key === errorKey)
    callback?.(error)
  })
}

export default useOnError
