import { useCallback, useState } from 'react'
import { useTranslations } from './index'
import { AnyObject, ObjectSchema, StringSchema } from 'yup'

export const useValidatorString = (
  validator: StringSchema<string, AnyObject, undefined, ''>,
): [
  string | undefined,
  (text?: string) => void,
  (text?: string) => boolean,
] => {
  const t = useTranslations()
  const [validationError, setValidationError] = useState<string | undefined>(
    undefined,
  )

  const validate = useCallback((text?: string) => {
    validator
      .validate(text, { abortEarly: true })
      .then(() => {
        setValidationError(undefined)
      })
      .catch(e => {
        e.errors?.[0] && setValidationError(t(e.errors?.[0]))
      })
  }, [])

  const isValid = useCallback((text?: string) => {
    return validator.isValidSync(text)
  }, [])

  return [validationError, validate, isValid]
}

export const useValidatorObject = (
  validator: ObjectSchema<AnyObject>,
): [
  string | undefined,
  (object?: AnyObject) => void,
  (object1?: AnyObject) => boolean,
] => {
  const t = useTranslations()
  const [validationError, setValidationError] = useState<string | undefined>(
    undefined,
  )

  const validate = useCallback((object?: AnyObject) => {
    validator
      .validate(object, { abortEarly: true })
      .then(data => {
        setValidationError(undefined)
      })
      .catch(e => {
        e.errors?.[0] && setValidationError(t(e.errors?.[0]))
      })
  }, [])

  const isValid = useCallback((object?: AnyObject) => {
    return validator.isValidSync(object)
  }, [])

  return [validationError, validate, isValid]
}
