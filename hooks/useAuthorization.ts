import { useNavigationContainerRef, useRootNavigationState, useRouter } from 'expo-router'
import {
  appService,
  authorizationService,
  notificationService,
  onAuth,
  savingPlanService,
  tokenService,
} from '@services'
import useSavingPlanStore from '@store/saving-plan-store'
import { catchError, forkJoin, of, switchMap } from 'rxjs'
import { SavingPlanProps } from '@factory/saving-plan-settings'
import { useObserverCallback } from './index'
import { map } from 'rxjs/operators'
import { AuthState } from '../services/authorization/AuthorizationService'
import { useEffect } from 'react'
import { mapSavingResponseToSavingStore } from '../utils/mapSavingResponseToSavingStore'

const useOnAuthorizationChange = (isLoaded: boolean): any => {
   const router = useRouter()

  useEffect(() => {
    if ( isLoaded) {
      authorizationService.setAuthStatePending()
      tokenService.loadToken()
      appService.setIsAppReady(true)
      if (tokenService.token?.length) {
        authorizationService.authorize()
      } else {
        authorizationService.unAuthorize()
      }
    }
 }, [ isLoaded])

 const savingStore = useSavingPlanStore()

  useObserverCallback<{
    isAuthenticated: boolean
    savingPlan: boolean | SavingPlanProps
  }>(
    authorizationService.getObserver().pipe(
      onAuth(),
      map(({ authenticated, accessTokenExists })=> authenticated === AuthState.Authenticated && accessTokenExists),
      switchMap(isAuthenticated => {
        if(isAuthenticated) {
          return forkJoin({
            isAuthenticated: of(isAuthenticated),
            savingPlan: savingPlanService.getUserSavingPlan().pipe(catchError(()=> of(false))),
          })
        }

        return forkJoin({
          isAuthenticated: of(isAuthenticated),
          savingPlan: of(false),
        })
      }),
    ),
    {
      next: ({ isAuthenticated,  savingPlan }) => {
        console.log('next',isAuthenticated,savingPlan)

        if (isAuthenticated && !savingPlan) {
          console.log('replace to login screen')
          router.replace('/(saving-plan)')
        }

        if (isAuthenticated && savingPlan) {
          mapSavingResponseToSavingStore(savingPlan, savingStore)
          router.replace('/(authenticated)')
        }

        if (!isAuthenticated) {
          console.log('replace to init screen');
          router.replace('/init-screen')
        }
        notificationService.hideModal()
      },
      complete:()=>{
        notificationService.hideModal()
      },
      error:()=>{
        console.log('error');
        notificationService.hideModal();
        router.replace('/init-screen')
      }
    },
  )
}

export default useOnAuthorizationChange
