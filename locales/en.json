{"common": {"logout": "Logout", "logOut": "Logout", "signIn": "Sign In", "signUp": "Sign Up", "moveForward": "Continue", "or": "Or", "continue": "Continue", "passwordForgotten": "Forgot password? ", "start": "Let's start", "introduction": "Introduction", "yes": "Yes", "no": "No", "verified": "Verified", "expires": "Expires", "notFound": "Not found"}, "exitDialog": {"title": "Are you sure you want to stop?", "description": "Your progress will be saved. You can come back and finish the setup process anytime!"}, "messages": {"authenticationError": "Bad login or password", "registrationError": "Error during registration process", "urlError": "Error: URL link not provided", "noAccountFound": "Can't find account with given email", "loggingOut": "Log out", "loading": "Loading...", "activationLinkSent": "We have sent the activation link to your e-mail!", "accountActivated": "Twoje konto zostało aktywowane, moż<PERSON>z się zalogować!"}, "validators": {"validationCheck": {"minLengthCapital": "At least one capital letter", "minLengthSmallLetter": "At least one lowercase letter", "requiredOneDigit": "At least one digit", "requiredAtLeastOneSpecialCharacter": "At least 1 special character", "minLength": "Minimum 12 characters", "maxLength": "Maximum 24 characters"}, "password": {"required": "Password is required", "min": "Password must be at least 12 characters long", "max": "Password cannot exceed 24 characters", "passwordStrength": "Password must contain at least one big letter", "passwordSpecialSign": "Password must contain at least one special sign", "passwordsMustMatch": "Password must match"}, "email": {"required": "email is required", "emailValidator": "Please provide valid email address"}}, "screens": {"home": {"title": "Use your spending to invest wisely."}}, "signIn": {"formProps": {"title": "Sign In", "subtitle": "Use your credentials to sign in", "bottomText": {"text": "Don't have an account?", "linkText": "Sign Up now"}}, "inputs": {"email": {"label": "Enter email"}, "password": {"label": "Password"}}, "buttons": {"signIn": {"label": "Sign in", "signInByGoogleAccount": "Use google account"}}, "forgotPassword": "Forgot password?"}, "signUp": {"formProps": {"title": "Sign Up", "bottomText": {"text": "Already have an account?", "linkText": "Sign In"}}, "inputs": {"username": {"label": "Email"}, "password": {"label": "Password"}, "confirmPassword": {"label": "Confirm password"}, "termsAndConditions": {"label": "I accept terms and conditions"}}, "buttons": {"signUp": {"label": "Sign Up"}, "moveForward": {"label": "Move forward"}}, "validationMessages": {"emailValidationMessage": "Please, provide a valid e-mail address", "passwordValidationMessage": "Password should contain 8 characters, 1 number and 1 uppercase letter", "confirmPasswordValidationMessage": "Password are not the same"}}, "termsOfUse": {"title": "Zgody"}, "requestInfo": {"success": "Success"}, "buttonsLabels": {"dismiss": "<PERSON><PERSON><PERSON>"}, "security": {"buttonLabel": "Add security phrase", "title": "Security phrase and private key", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."}, "savingPlan": {"formProps": {"title": "Savings tailored to your lifestyle!"}, "howItWorks": {"title": "How does our Savings plan work?", "description": "This is your way to save in Bitcoin – tailored to your needs and lifestyle. You have full control over how you want to set aside funds."}, "whyItIsWorth": {"title": "Why is it worth it?", "description": "Thanks to automation and adaptation to your habits, saving becomes simple and effective. You eliminate emotions, build results over time, and have full control over your plan."}, "callToAction": {"text": "Small steps, big results – start now!"}, "savingGoalSelection": {"title": "Choose what you want to save for", "infoBox": "Selecting your main goal will help us tailor future tips and suggestions just for you!"}, "nameStep": {"title": "Name your savings plan", "info": "Give your plan a name — create something that suits you!", "inputLabel": "Plan name", "placeholder": "e.g., First apartment"}, "timeHorizonStep": {"title": "Set your time horizon", "info": "Decide how long you want to run your savings plan. Adjust the time horizon to match your goals.", "unit": {"one": "year", "few": "years", "many": "years"}}, "plans": {"title": "Set your savings plan", "adjusted": {"title": "Adjusted to Your Life", "description": "A plan perfectly tailored to your financial habits. We analyze your spending and automatically adjust your savings amount so it's comfortable and almost unnoticeable. Saving becomes simple and natural, without sacrifice.", "tags": ["Premium", "Recommended", "Safer"], "message": "Great! Saving will become easy, and your habits will support your financial goals."}, "simple": {"title": "Simple Saving", "description": "You decide how much and how often to save — weekly, monthly, or at your own pace. It’s a convenient way to reach your goals or build a financial cushion, always on your own terms.", "tags": ["Free", "Not recommended", "Less safe"], "message": "Think carefully before choosing the amount! If you're unsure, consider selecting the first plan."}}, "amountStep": {"title": "Choose your amount and rhythm", "amountLabel": "Saving amount", "frequencyLabel": "How often do you want to save?", "unit": "PLN", "frequencies": {"week": "Week", "month": "Month", "quarter": "Quarter"}}, "ignoreTransactionsStep": {"title": "Do you want to ignore larger transactions?", "inputLabel": "Amount above which transactions will be ignored", "unit": "PLN", "message": "Setting the value too low may result in many transactions being skipped, which can reduce the effectiveness of your savings. We recommend choosing a value that ensures regular and effective saving."}, "adjustedPlan": {"title": "Set a Saving Percentage or Amount", "percentagePlan": {"heading": "Percentage of Each Transaction Amount", "description": "By choosing this option, you define a percentage of each transaction's value that will be automatically transferred to your savings account. For example, with a 5% setting, from a transaction worth 100 PLN, 5 PLN will go into your savings.", "recommendation": "It is recommended to set a value between 2% and 7%. These amounts allow for regular saving without noticeably affecting daily expenses.", "alert": "Setting such a high percentage may significantly impact the availability of funds for daily spending. Are you sure you want to continue?"}, "fixedPlan": {"heading": "Fixed Amount per Transaction", "description": "This option allows you to specify a fixed amount that will be automatically saved to your savings account with each transaction. For example, with a 5 PLN setting, 5 PLN will be saved from every transaction, regardless of its value.", "recommendation": "It is recommended to set an amount between 1 PLN and 10 PLN. These values allow for regular saving without putting too much strain on your budget."}}, "summary": {"content": {"timeHorizon": "Time Horizon:", "savingType": "Saving Method:", "savingPercentage": "Saving Percentage:", "ignoreTransaction": "Ignore Larger Transactions:", "ignoreTransactionPrefix": "Yes, above "}, "buttons": {"createPlan": "Create Plan", "changePlan": "Change", "connect-bank": "Connect to bank account"}}}, "bank-connection": {"buttonTitle": "Add new bank account", "title": "Connected bank accounts", "description": "Lorem ipsum odor amet.", "details": "Account details", "disconect": "Disconnect", "renew": "Renew connection", "connectionDate": "Connection date", "lastRenew": "Last renewal", "expiryDate": "Connection expires", "renewConnection": "Renew connection", "noAccounts": "No connected bank accounts. Add a new one using the button below", "expiryTooltip": "When less than 30 days remain until the connection expires", "discconectModal": {"title": "Confirm disconnection of bank account", "description": "Once disconnected, you will no longer be able to use this bank account in our app and all related data will be removed.", "cancel": "Cancel", "confirm": "Yes, disconnect"}}, "viewKeyModal": {"title": "Enter password to display", "description": "To display your account number key, you need to confirm your identity."}, "dashboard": {"greeting": "Hello", "totalBalance": "Total Balance", "configurationBanner": {"text": "Complete your account setup\nand start saving to the fullest!", "button": "Take me there!", "highlightWord": "saving"}, "verificationBanner": {"text": "Verify your identity\nto unlock all features!", "button": "Verify now", "highlightWord": "identity"}, "sections": {"savings": "Your Savings", "transactions": "Recent Transactions"}, "savingsChart": {"legend": {"savingsValue": "Savings Value", "depositedAmount": "Deposited PLN"}}, "deposited": "deposited", "buttons": {"seeAll": "See All"}, "transactions": {"types": {"deposit": "<PERSON><PERSON><PERSON><PERSON>"}}}}