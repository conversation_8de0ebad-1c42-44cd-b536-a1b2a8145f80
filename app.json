{"expo": {"name": "reti", "slug": "reti", "version": "1.0.0", "orientation": "portrait", "backgroundColor": "#FFF", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/img.png", "resizeMode": "cover", "backgroundColor": "#FFF"}, "assetBundlePatterns": ["**/*"], "runtimeVersion": "1.0.0", "ios": {"supportsTablet": true, "infoPlist": {"CFBundleAllowMixedLocalizations": true, "ITSAppUsesNonExemptEncryption": false}, "bundleIdentifier": "tech.reti"}, "android": {"edgeToEdge": true, "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFF"}, "package": "com.reti.tech"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "locales": {"pl": "./translations/pl.json", "en": "./translations/en.json"}, "plugins": [["@react-native-google-signin/google-signin", {"iosUrlScheme": "com.googleusercontent.apps.tech.reti"}], "expo-router", "expo-localization", "expo-secure-store"], "extra": {"router": {"origin": false}, "eas": {"projectId": "daf51ce1-98c4-46ac-9b60-79880969cef3"}}, "updates": {"url": "https://u.expo.dev/daf51ce1-98c4-46ac-9b60-79880969cef3"}}}