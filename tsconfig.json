{"extends": "expo/tsconfig.base", "compilerOptions": {"allowJs": false, "strict": true, "noEmit": true, "skipLibCheck": true, "baseUrl": "./", "paths": {"@components": ["components/index"], "@ui/*": ["components/ui/*"], "@hooks": ["hooks/index"], "@utils": ["utils/index"], "@locales/*": ["locales/*"], "@providers": ["providers/index"], "@theme": ["theme/index"], "@services": ["services/index"], "@typings/*": ["typings/*"], "@factory/*": ["factory/*"], "@const/*": ["const/*"], "@operators/*": ["services/operators/*"], "@store/*": ["store/*"]}}, "include": ["app", "components", "hooks", "utils", "services", "theme", "typings", "store"], "exclude": ["node_modules", "node_modules/**", "**/node_modules/*", "babel.config.js", "metro.config.js", "jest.config.js"]}