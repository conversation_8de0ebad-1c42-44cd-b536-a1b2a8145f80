import { createWithEqualityFn } from 'zustand/traditional'
import { Agreement } from '@factory/terms'

interface RegistrationState {
  allChecked: boolean
  username: string
  password: string
  regulations_accepted?: boolean
  agreements?: Map<string, Agreement>
  agreementsArray?: Agreement[]
  setUsername: (username: string) => void
  setPassword: (password: string) => void
  setLoginAndPassword: (username: string, password: string) => void
  setTerms: (terms: boolean) => void
  reset: () => void
  setAgreements: (agreements: Agreement[]) => void
  setAgreement: (name: string, checked: boolean) => void
  setAllChecked: () => void
}

const useRegistrationStore = createWithEqualityFn<RegistrationState>(
  (set, get) => ({
    allChecked: false,
    username: '',
    password: '',
    regulations_accepted: false,
    agreements: undefined,

    setUsername: username => set({ username }),
    setPassword: password => set({ password }),
    setLoginAndPassword: (username: string, password: string) =>
      set({ username, password }),
    setTerms: regulations_accepted => set({ regulations_accepted }),
    setAgreements: (agreements: Agreement[]) => {
      const map: Map<string, Agreement> = new Map()

      agreements.forEach(element => {
        map.set(element.name, { ...element, checked: false })
      })

      set({ agreements: map, agreementsArray: Array.from(map.values()) })
    },
    setAgreement: (name: string, checked: boolean) => {
      const { agreements } = get()
      const map = new Map(agreements)
      if(agreements && agreements.get(name)) {
        map.set(name, { ...agreements.get(name) as Agreement, checked })
        set({ agreements: map, agreementsArray: Array.from(map.values()) })
      }
    },
    setAllChecked: () => {
      const { agreements, allChecked } = get()

      const map = new Map(agreements)

      Array.from(map.keys()).forEach(element => {
        map.set(element, { ...map.get(element) as Agreement, checked: !allChecked })
      })

      set({
        agreements: map,
        agreementsArray: Array.from(map.values()),
        allChecked: !allChecked,
      })
    },
    reset: () =>
      set({ username: '', password: '', regulations_accepted: false }),
  }),
)

export default useRegistrationStore
