import { create } from 'zustand'

const initialState = {
  login: null,
  password: null,
  error: null,
}
const validateEmail = (email: string) => {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  return emailRegex.test(email)
}

const validatePassword = (password: string) => {
  // Simple password validation: at least 8 characters long
  return password.length >= 8
}

const loginStore = create(set => ({
  ...initialState,

  login: (email: string, password: string) => {
    if (!validateEmail(email)) {
      set({ error: 'Invalid email' })
      return
    }

    if (!validatePassword(password)) {
      set({ error: 'Invalid password' })
      return
    }

    // Simulate authentication logic here
    const token = 'your-token-here'
    set({ authenticated: true, token })

    // Clear the error after successful login
    setTimeout(() => {
      set({ error: null })
    }, 2000)
  },

  validateEmail(email: string) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    return emailRegex.test(email)
  },

  validatePassword(password: string) {
    // Simple password validation: at least 8 characters long
    return password.length >= 8
  },
}))

export default loginStore
