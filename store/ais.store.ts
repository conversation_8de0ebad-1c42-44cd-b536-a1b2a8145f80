import { createWithEqualityFn } from 'zustand/traditional'
import { persist } from 'zustand/middleware'
import * as SecureStore from 'expo-secure-store'
import { BankAccount, AisProcess } from '@factory/ais-process'

const STORAGE_NAME = 'ais-process-store'

interface AisProcessStoreState {
  bankAccounts: BankAccount[]
  currentProcess: AisProcess | null
}

interface AisProcessStoreActions {
  setBankAccounts: (accounts: BankAccount[]) => void
  setCurrentProcess: (process: AisProcess) => void
  clearBankAccounts: () => void
  clearCurrentProcess: () => void
}

export type AisProcessStoreProps = AisProcessStoreState & AisProcessStoreActions

const zustandSecureStorage = {
  getItem: async (name: string) => {
    return await SecureStore.getItemAsync(name)
  },
  setItem: async (name: string, value: string) => {
    await SecureStore.setItemAsync(name, value)
  },
  removeItem: async (name: string) => {
    await SecureStore.deleteItemAsync(name)
  },
}

const useAisProcessStore = createWithEqualityFn(
  persist<AisProcessStoreProps>(
    (set) => ({
      bankAccounts: [],
      currentProcess: null,
      setBankAccounts: (bankAccounts) => {
        set({ bankAccounts })
      },
      setCurrentProcess: (currentProcess) => {
        set({ currentProcess })
      },
      clearBankAccounts: () => {
        set({ bankAccounts: [] })
      },
      clearCurrentProcess: () => {
        set({ currentProcess: null })
      }
    }),
    {
      name: STORAGE_NAME,
      storage: zustandSecureStorage,
      partialize: (state: AisProcessStoreProps) => ({
        bankAccounts: state.bankAccounts,
        currentProcess: state.currentProcess
      }),
    }
  )
)

export const clearAisProcessStore = () => {
  useAisProcessStore.persist.clearStorage()
}

export default useAisProcessStore