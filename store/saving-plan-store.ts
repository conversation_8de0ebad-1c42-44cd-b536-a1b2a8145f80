import { createWithEqualityFn } from 'zustand/traditional'
import { Target } from '@factory/saving-plans-target'
import { persist } from 'zustand/middleware'
import * as SecureStore from 'expo-secure-store'

const STORAGE_NAME = 'saving-plan-store'

export type Frequency = 'week' | 'month' | 'quarter'

export enum PlanType {
  Percentage = 'PR',
  Amount='AM',
  ReccuringPurchase = 'RP'
}

export interface SavingPlanStoreState {
  planName?: string
  adjustedPlanOption?:string
  adjustedPlanPercentage: number
  fixedPlanValue:number
  selectedTarget?: Target
  timeHorizon: number
  ignoreTransactionsAbove: number
  savingAmountAndFrequency: {
    amount: number
    frequency: Frequency | null
  }
  selectedPlan?: PlanType
}

export interface SavingPlanStoreActions {
  setAdjustedPlanPercentage:(percentage: number) => void
  setFixedPlanValue:(value: number) => void
  setPlanName: (planName: string) => void
  setTarget: (target: Target) => void
  setTimeHorizon: (timeHorizon: number) => void
  setIgnoreTransactionsAbove: (amount: number) => void
  setSavingAmountAndFrequency: (data: {
    amount: number
    frequency: Frequency | null
  }) => void
  setSelectedPlan:(planType: PlanType) => void
  setAdjustedPlanOption:(adjustedPlanOptionName: string) => void
}

export type SavingPlanStoreProps = SavingPlanStoreState & SavingPlanStoreActions

const zustandSecureStorage = {
  getItem: async (name: string) => {
    return await SecureStore.getItemAsync(name)
  },
  setItem: async (name: string, value: string) => {
    await SecureStore.setItemAsync(name, value)
  },
  removeItem: async (name: string) => {
    await SecureStore.deleteItemAsync(name)
  },
}


const useSavingPlanStore = createWithEqualityFn(
  persist<SavingPlanStoreProps>(
    (set) => ({
      planName: undefined,
      adjustedPlanOption: undefined,
      selectedPlan: undefined,
      selectedTarget: undefined,
      timeHorizon: 1,
      ignoreTransactionsAbove: 0,
      savingAmountAndFrequency: {
        amount: 0,
        frequency: null,
      },
      adjustedPlanPercentage: 2,
      setAdjustedPlanPercentage: (percentage: number) => {
        set({ adjustedPlanPercentage: percentage })
      },
      fixedPlanValue: 5,
      setFixedPlanValue: (value: number) => {
        set({ fixedPlanValue: value })
      },
      setPlanName: (planName: string) => {
        set({ planName })
      },
      setSelectedPlan: (planType: PlanType) => {
        set({ selectedPlan: planType })
      },
      setTarget: (target: Target) => {
        set({ selectedTarget: target })
      },
      setAdjustedPlanOption: (adjustedPlanOptionName: string) => {
        set({ adjustedPlanOption: adjustedPlanOptionName })
      },
      setTimeHorizon: (timeHorizon) => set({ timeHorizon }),
      setIgnoreTransactionsAbove: (amount) =>
        set({ ignoreTransactionsAbove: amount }),
      setSavingAmountAndFrequency: (data) =>
        set({ savingAmountAndFrequency: data }),
    }),
    {
      name: STORAGE_NAME,
      storage: zustandSecureStorage,
      partialize: (state: SavingPlanStoreProps) => ({
        planName: state.planName,
        adjustedPlanOption: state.adjustedPlanOption,
        selectedPlan: state.selectedPlan,
        selectedTarget: state.selectedTarget,
        timeHorizon: state.timeHorizon,
        ignoreTransactionsAbove: state.ignoreTransactionsAbove,
        savingAmountAndFrequency: state.savingAmountAndFrequency,
        adjustedPlanPercentage: state.adjustedPlanPercentage,
        fixedPlanValue: state.fixedPlanValue,
      }),
    }
  )
)

export const clearSavingPlanStore = ()=> {
  useSavingPlanStore.persist.clearStorage()
}

export default useSavingPlanStore
