import { BehaviorSubject } from 'rxjs'
import { auth, IRefreshToken, ITokenResponse } from '@factory/auth'
import * as SecureStore from 'expo-secure-store'
import { SecureStorageKeys } from '@const/secureStorageKeys'
import { errorHandlerService, ErrorKey } from '../index'
import { fromApi } from '@operators/fromApi'

class TokenService {
  private readonly tokenBehaviour = new BehaviorSubject<
    ITokenResponse | undefined
  >(undefined)

  getObserver() {
    return this.tokenBehaviour
  }

  loadToken() {
    try {
      const storageAccessToken = SecureStore.getItem(
        SecureStorageKeys.AUTH_TOKEN,
      )
      const storageRefreshToken = SecureStore.getItem(
        SecureStorageKeys.REFRESH_TOKEN,
      )

      if (storageAccessToken) {
        this.tokenBehaviour.next({
          access: storageAccessToken,
          refresh: storageRefreshToken as string,
        })

        return storageAccessToken
      }
    } catch (e) {
      errorHandlerService.add(ErrorKey.TokenError, e as string)
    }
  }

  get token(): string | undefined {
    const token = this.tokenBehaviour.getValue()?.access

    if (!token) {
      return this.loadToken()
    }

    return token
  }

  setToken(response: ITokenResponse) {
    SecureStore.setItem(SecureStorageKeys.AUTH_TOKEN, response.access)
    SecureStore.setItem(SecureStorageKeys.REFRESH_TOKEN, response.refresh)
    this.tokenBehaviour.next(response)
  }

  refreshToken() {
    const refreshStorageToken = SecureStore.getItem(
      SecureStorageKeys.REFRESH_TOKEN,
    )
    if (refreshStorageToken) {
      const data: IRefreshToken = { refresh: refreshStorageToken }
      fromApi(auth.refreshToken.post(data)).subscribe(response => {
        console.log('refresh token',response)
      })
    }
  }

  async clearTokens() {
    await this.clearAuthToken()
    await this.clearRefreshToken()
    this.tokenBehaviour.next(undefined)
  }

  private async clearAuthToken() {
    await SecureStore.deleteItemAsync(SecureStorageKeys.AUTH_TOKEN)
  }

  private async clearRefreshToken() {
    await SecureStore.deleteItemAsync(SecureStorageKeys.REFRESH_TOKEN)
  }
}

const tokenService = new TokenService()

export default tokenService
