import { IRegister, register } from '@factory/register'
import { fromApi } from '@operators/fromApi'

class RegistrationService {
  createAccount(data: IRegister) {
    return fromApi(register.createAccount.post(data))
  }

  sendActivationCode(code: string, username: string) {
    return from<PERSON>pi(register.registrationConfirm.path(code, username))
  }

  resendActivationCode( username: string) {
    return fromApi(register.registrationConfirm.post(username))
  }
}

export default new RegistrationService()
