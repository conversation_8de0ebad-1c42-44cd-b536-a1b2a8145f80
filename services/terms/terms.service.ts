import { fromApi } from '@services'
import { termsAndConditions, TermsOfUseResponse, TermsPostData } from '@factory/terms'
import { BehaviorSubject, Observable, of, switchMap, tap } from 'rxjs'
import { SecureStorageKeys } from '@const/secureStorageKeys'
import * as SecureStore from 'expo-secure-store'

class TermsService {
  termsObserver = new BehaviorSubject<TermsOfUseResponse | null>(null)

  getTerms() :Observable<TermsOfUseResponse|null> {
    return this.termsObserver.pipe(
      switchMap(term => {
        if (term === null) {
          return (
            fromApi(termsAndConditions.get()).pipe(
              switchMap(value => {
                this.termsObserver.next(value)
                SecureStore.setItem(SecureStorageKeys.TERMS_VERSION, value.version)
                return of(value)
              }),
            )
          )
        }
        return of(term)
      }),
    )
  }

  postTerms(data: TermsPostData) {
    return fromApi(termsAndConditions.post(data))
  }
}

export default new TermsService()
