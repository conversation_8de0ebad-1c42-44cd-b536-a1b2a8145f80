import { BehaviorSubject } from 'rxjs'

export interface IModal {
  visible: boolean
  message?: string
}

class NotificationService {
  modalBehavior = new BehaviorSubject<IModal>({
    visible: false,
  })

  get modalObserver() {
    return this.modalBehavior.asObservable()
  }
  showModal(message?: string) {
    this.modalBehavior.next({ visible: true, message })
  }

  hideModal() {
    this.modalBehavior.next({ visible: false, message: undefined })
  }
}

const notificationService = new NotificationService()
export default notificationService
