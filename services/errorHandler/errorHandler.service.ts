import { BehaviorSubject } from 'rxjs'

export enum ErrorKey {
  AuthError = 'AuthError',
  RegistrationError = 'RegistrationError',
  TokenError = 'TokenError',
}

export type AppError = {
  key: ErrorKey
  errorText?: string
  status?: number,
  translate: boolean
}

class ErrorHandlerService {
  private readonly errorHandlerBehaviour = new BehaviorSubject<AppError[]>([])

  getObserver() {
    return this.errorHandlerBehaviour.asObservable()
  }

  add(key: ErrorKey, errorText?: string, status?: number, translate = true) {
    this.errorHandlerBehaviour.next([
      ...this.errorHandlerBehaviour.getValue(),
      { key, errorText, status , translate},
    ])
  }

  clearError(key: ErrorKey) {
    const hasError = this.errorHandlerBehaviour
      .getValue()
      .some(error => error.key === key)

    if (hasError) {
      const next = this.errorHandlerBehaviour
        .getValue()
        .filter(errors => errors.key !== key)
      this.errorHandlerBehaviour.next(next)
    }
  }

  clearAllErrors() {
    this.errorHandlerBehaviour.next([])
  }
}

const errorHandlerService = new ErrorHandlerService()

export default errorHandlerService
