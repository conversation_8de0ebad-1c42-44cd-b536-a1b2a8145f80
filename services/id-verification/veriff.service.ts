import VeriffSdk from '@veriff/react-native-sdk';
import { first, from, Subject } from 'rxjs'
import { veriffFactory, VeriffSessionProps } from '@factory/veriff'

class VerificationService {
  veriffSession = new Subject<VeriffSessionProps>();


  createSession(){
    from(veriffFactory.veriffSession.createVerificationSession())
      .pipe(first())
      .subscribe((data)=> this.veriffSession.next(data));
  }

}

const verificationService = new VerificationService()
export default verificationService
