import { BehaviorSubject } from 'rxjs'

interface IAppBehavior {
  isAppReady: boolean
}

class AppService {
  private readonly appBehavior = new BehaviorSubject<IAppBehavior>({
    isAppReady: false,
  })

  setIsAppReady(isAppReady: boolean) {
    this.appBehavior.next({ ...this.appBehavior.getValue(), isAppReady })
  }

  getObserver(): BehaviorSubject<IAppBehavior> {
    return this.appBehavior
  }
}

export default new AppService()
