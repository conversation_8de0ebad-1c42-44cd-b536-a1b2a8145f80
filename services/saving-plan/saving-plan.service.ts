import * as SecureStore from 'expo-secure-store'
import { SecureStorageKeys } from '@const/secureStorageKeys'
import { fromApi } from '@operators/fromApi'
import { savingPlanTarget, Target } from '@factory/saving-plans-target'
import { map } from 'rxjs/operators'
import { savingPlanSettings } from '@factory/saving-plan-settings'
import { SavingPlanStoreProps } from '@store/saving-plan-store'
import { mapSavingStoreDataToPostSaving } from '@utils'
import { catchError, of } from 'rxjs'

const mapIcon = (iconLabel: string): string => {
  return (
    [
      { icon: 'dollar', title: 'Wkład własny' },
      { icon: 'car-rental', title: 'Samochód' },
      { icon: 'modern-house', title: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
      { icon: 'finance', title: 'Emerytura' },
      { icon: 'finance', title: 'Edukacja' },
    ].filter(element => element.title === iconLabel)?.[0]?.icon || ''
  )
}

class SavingPlanService {
  getSavingPlansTarget() {
    return fromApi(savingPlanTarget.get()).pipe(
      map(response => response.targets?.[0]),
      map((targets: Target[]) =>
        targets.map((targetElement, index) => ({
          ...targetElement,
          icon: mapIcon(targetElement.icon_label),
        })),
      ),
      // catchError(error => {
      //   console.log('error', error.response)
      //   return of(error.response)
      // }),
    )
  }

  setSavingPlanCreated() {
    SecureStore.setItem(SecureStorageKeys.IS_SAVING_PLAN_CREATED, 'true')
  }

  isSavingPlanCreated() {
    return Boolean(
      SecureStore.getItem(SecureStorageKeys.IS_SAVING_PLAN_CREATED),
    )
  }

  setSavingPlanTarget(target: string) {
    return fromApi(savingPlanTarget.post(target))
  }

  checkIsUserCreatedPlan() {
    return fromApi(savingPlanSettings.path())
  }

  getUserSavingPlan() {
    return fromApi(savingPlanSettings.get())
  }

  saveUserPlan(savingPlanData: SavingPlanStoreProps) {
    const postData = mapSavingStoreDataToPostSaving(savingPlanData)
    return fromApi(savingPlanSettings.post(postData))
  }
}

export default new SavingPlanService()
