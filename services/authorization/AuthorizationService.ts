import { BehaviorSubject, delay } from 'rxjs'
import { auth, IAuth } from '@factory/auth'
import {
  tokenService,
} from '../index'
import * as SecureStore from 'expo-secure-store'
import { SecureStorageKeys } from '@const/secureStorageKeys'
import { fromApi } from '@operators/fromApi'
import { password } from '@factory/password'

export enum AuthState {
  Pending = 'Pending',
  Unknown = 'Unknown',
  Authenticated = 'Authenticated',
  Unauthenticated = 'Unauthenticated',
}

export interface IAuthorization {
  authenticated: AuthState
}

const DEBOUNCE_TIME_MS = 800

class AuthorizationService {
  private readonly authorizationSubject = new BehaviorSubject<IAuthorization>({
    authenticated: AuthState.Unknown,
  })

  signIn(authorizationData: IAuth) {
    this.setAuthStatePending()
    return fromApi(auth.token.post(authorizationData))
      .pipe(delay(DEBOUNCE_TIME_MS))
  }

  setAuthStatePending() {
    this.authorizationSubject.next({ authenticated: AuthState.Pending })
  }

  authorize() {
    this.authorizationSubject.next({ authenticated: AuthState.Authenticated })
  }

  async unAuthorize() {
    this.setAuthStatePending()
    setTimeout(() => {
      tokenService.clearTokens()
      this.authorizationSubject.next({
        authenticated: AuthState.Unauthenticated,
      })
    }, DEBOUNCE_TIME_MS)
  }

  getObserver() {
    return this.authorizationSubject
  }

  isAccountCreated() {
    return Boolean(SecureStore.getItem(SecureStorageKeys.IS_ACCOUNT_CREATED))
  }

  setIsAccountCreated(isAccountCreated: boolean) {
    SecureStore.setItem(
      SecureStorageKeys.IS_ACCOUNT_CREATED,
      isAccountCreated.toString(),
    )
  }

  resetPassword(email: string) {
    password.reset.post({ email })
  }
}

const authorizationService = new AuthorizationService()

export default authorizationService
