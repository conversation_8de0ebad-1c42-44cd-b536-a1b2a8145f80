// ais-process.service.ts
import { fromApi } from '@operators/fromApi'
import { BehaviorSubject, Observable, of, switchMap, tap } from 'rxjs'
import { aisProcessApi, AisProcess, AisAccountResponse, BankAccount } from '@factory/ais-process'
import useAisProcessStore from '@store/ais.store'

class AisProcessService {
  private bankAccountsObserver = new BehaviorSubject<BankAccount[]>([])
  private store = useAisProcessStore.getState()

  createProcess(): Observable<AisProcess> {
    return fromApi(aisProcessApi.post()).pipe(
      tap((newProcess) => {
        this.store.clearCurrentProcess()
      })
    )
  }

  getBankAccounts(): Observable<BankAccount[]> {
    return this.bankAccountsObserver.pipe(
      switchMap(accounts => {
        if (accounts.length === 0 && !this.store.currentProcess) {
          return fromApi<AisAccountResponse[]>(aisProcessApi.get()).pipe(
            switchMap(responses => {
              const allAccounts: BankAccount[] = []
              const allProcesses: AisProcess[] = []
              responses.forEach(response => {
                if (response.accounts) {
                  allAccounts.push(...response.accounts)
                }
                if (response.process_id) {
                  allProcesses.push({
                    process_id: response.process_id
                  } as AisProcess)
                }
              })
              // Logic here is AIS Specific, might change in the future
              if (allProcesses.length > 0) {
                this.store.setCurrentProcess({ 
                  process_id: allProcesses[0].process_id,
                  url: ''
  })              }
              this.store = useAisProcessStore.getState()
              return of(allAccounts)
            })
          )
        }
        return of(accounts)
      })
    )
  }

  clearBankAccounts() {
    this.bankAccountsObserver.next([])
    this.store.clearCurrentProcess()
    this.store.clearBankAccounts()
  }
}

const aisProcessService = new AisProcessService()
export default aisProcessService