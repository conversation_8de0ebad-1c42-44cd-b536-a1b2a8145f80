import { combineLatest, filter, map, mergeMap, Observable } from 'rxjs'
import { appService, tokenService } from '@services'
import {
  AuthState,
  IAuthorization,
} from '../authorization/AuthorizationService'

export interface IOnAuth {
  authenticated: AuthState
  accessTokenExists: boolean
}

export function onAuth() {
  return function (source: Observable<IAuthorization>): Observable<IOnAuth> {
    return source.pipe(
      mergeMap(({ authenticated }) =>
        combineLatest([
          appService.getObserver(),
          tokenService.getObserver(),
        ]).pipe(
          map(([{ isAppReady }, token]) => ({
            isAppReady,
            authenticated,
            accessTokenExists: token?.access !== undefined,
          })),
        ),
      ),
      filter(
        ({ isAppReady, authenticated }) =>
          isAppReady &&
          (authenticated === AuthState.Authenticated ||
            authenticated === AuthState.Unauthenticated),
      ),
      map(({ authenticated, accessTokenExists }) => ({
        authenticated,
        accessTokenExists,
      })),
    )
  }
}
