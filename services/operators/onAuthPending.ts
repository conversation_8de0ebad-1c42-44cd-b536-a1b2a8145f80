import { combineLatest, filter, map, mergeMap, Observable } from 'rxjs'
import { appService, tokenService } from '@services'
import {
  AuthState,
  IAuthorization,
} from '../authorization/AuthorizationService'

export function onAuthPending() {
  return function (source: Observable<IAuthorization>): Observable<boolean> {
    return source.pipe(
      mergeMap(({ authenticated }) =>
        combineLatest([
          appService.getObserver(),
          tokenService.getObserver(),
        ]).pipe(
          map(([{ isAppReady }, token]) => ({
            isAppReady,
            authenticated,
            accessTokenExists: token?.access !== undefined,
          })),
        ),
      ),
      filter(
        ({ isAppReady, authenticated }) =>
          isAppReady && authenticated === AuthState.Pending,
      ),
      map(() => true),
    )
  }
}
