import {
  combineLatest,
  distinctUntilChanged,
  filter,
  map,
  mergeMap,
  Observable,
  pairwise,
  tap,
} from 'rxjs'
import { appService, tokenService } from '@services'
import { IAuthorization } from '../authorization/AuthorizationService'

export function onInit() {
  return function (source: Observable<IAuthorization>): Observable<boolean> {
    return source.pipe(
      mergeMap(({ authenticated }) =>
        combineLatest([
          appService.getObserver(),
          tokenService.getObserver(),
        ]).pipe(
          map(([{ isAppReady }, token]) => ({
            isAppReady,
            authenticated: authenticated && token?.access !== undefined,
          })),
        ),
      ),
      pairwise(),
      filter(
        ([, { isAppReady, authenticated }]) => isAppReady && !authenticated,
      ),
      map(([, { authenticated }]) => authenticated),
      tap(p => {
        console.log('p', p)
      }),
    )
  }
}
