import { filter, Observable, pairwise } from 'rxjs'
import {
  AuthState,
  IAuthorization,
} from '../authorization/AuthorizationService'
import { map } from 'rxjs/operators'

export function onLogout() {
  return function (source: Observable<IAuthorization>): Observable<AuthState> {
    return source.pipe(
      pairwise(),
      filter(([prev, current]) => prev.authenticated && !current.authenticated),
      map(([, curr]) => curr),
      filter(
        ({ authenticated }) => authenticated === AuthState.Unauthenticated,
      ),
      map(({ authenticated }) => authenticated),
    )
  }
}
