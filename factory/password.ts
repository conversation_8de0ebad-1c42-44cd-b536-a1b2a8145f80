import { api } from './base'

export interface IResetPassword {
  email: string
}

export interface ITokenResetPostResponse {
  access: string
  refresh: string
}

export interface IResetPasswordTokenResponse {
  new_password: string
  confirm_password: string
}

const reset = {
  post: (data: IResetPassword): Promise<ITokenResetPostResponse> =>
    api.post('/reset-password/', data),
}

const resetPasswordConfirm = {
  post: (token: string): Promise<IResetPasswordTokenResponse> =>
    api.post(`/reset-password-confirm/${token}/`),
}

export const password = {
  reset,
  resetPasswordToken: resetPasswordConfirm,
}
