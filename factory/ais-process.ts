import { api } from './base'

const URL = '/ais-process/'

export enum VerificationStatus {
  INITIATED = 'INITIATED',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export interface BankAccount {
  accountId: string
  currency: string
  accountHolderType: string
  accountType: string
  name: string
  createDate: Date
  accountNumber: string
  schemeName: string
  nameClient: string
  holderName: string
  holderAddress: string
  holderNameAddress: string
  accountRelations: []
  balances: []
  auxData: []
  logo?: string
  connectionDate?: string
  renewalDate?: string
  expiryDate?: string
}

export interface AisProcess { // TODO: Fix return on backend service to sneakcase
  url?: string
  processId?: string
  process_id?: string
  verificationStatus?: VerificationStatus
  created_at?: string
  updated_at?: string
}
export interface AisAccountResponse {
  accounts: BankAccount[]
  process_id: string
}

export const aisProcessApi = {
  get: () => api.get<AisAccountResponse[]>(`${URL}get_accounts/`),
  post: () => api.post<AisProcess>(URL)
}