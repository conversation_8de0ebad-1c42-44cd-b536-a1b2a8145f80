import axios, { AxiosResponse } from 'axios'


const headers = {
  "x-auth-client": process.env.EXPO_PUBLIC_VERIFF_API_KEY,
  "x-hmac-signature" : process.env.EXPO_PUBLIC_VERIFF_API_SECRET,
  "content-type": "application/json",
};
export enum  DocumentContext {
  DocumentFront= "document-front",
  DocumentBack = "document-back",
  DocumentAndFace = "document-and-face",
  DocumentFace = "face"
}

export interface VeriffSessionProps {
  status: boolean,
  verification: {
  id: string,
    url: string,
    vendorData: string,
    host: string,
    status: string,
    sessionToken: string
}
}

const veriffSession = {
  createVerificationSession: (): Promise<AxiosResponse<VeriffSessionProps>> => {
    // see: https://devdocs.veriff.com/apidocs/v1sessions
    const url='/v1/sessions'
    return axios.post(`${process.env.EXPO_PUBLIC_VERIFF_BASE_URL}${url}`, {}, {headers: headers})
  },
  uploadConsent: (sessionId: string)=> {
    // see: https://devdocs.veriff.com/apidocs/v1sessionsidconsents
    const url='/v1/sessions/{sesssionId}/consents'

  },
  uploadMedia: (sessionId: string, data : { image: {context: DocumentContext, timestamp: string}}) =>  {
    // see : https://devdocs.veriff.com/apidocs/v1sessionsidmedia-3
   const url = `/v1/sessions/{session}/media`;
   axios.post(`${process.env.EXPO_PUBLIC_VERIFF_BASE_URL}${url}`, {data} ,{headers})
  },
  uploadEndUserRelatedData:()=> {
    // see: https://devdocs.veriff.com/apidocs/v1sessionsidcollected-data-1
    const url= '/v1/sessions/{id}/collected-data'
  },
  validateNationalIDNumber: ()=> {
    // see: https://devdocs.veriff.com/apidocs/v1validate-registry
    const url = '/v1/validate-registry'
  }
}

export const veriffFactory ={
  veriffSession
}
