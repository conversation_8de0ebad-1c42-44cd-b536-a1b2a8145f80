import { api } from './base'

export interface IRegister {
  username: string
  password: string
}

export interface IRegisterPostResponse {
  id: string
  username: string
}

export interface IConfirmGetResponse {
  message: string
}

const createAccount = {
  post: (data: IRegister) => {
    return api.post<IRegisterPostResponse>(`/register/`, data)
  },
}

const registrationConfirm = {
  path: (code: string, username: string) =>
    api.patch<IConfirmGetResponse>(`/confirm-registration-code/`, { code, username }),
  post: (username: string) => api.post(`/confirm-registration-code/`, { username }),
}

export const register = {
  createAccount,
  registrationConfirm,
}
