import { api } from './base'

export interface IAuth {
  username: string
  password: string
}

export interface ITokenResponse {
  access: string
  refresh: string
}

export interface IRefreshToken {
  refresh: string
}

const token = {
  post: (data: IAuth) => api.post<ITokenResponse>('/token/', data),
}

const refreshToken = {
  post: (data: IRefreshToken) => api.post('/refresh/', data),
}

export const auth = {
  token,
  refreshToken,
}
