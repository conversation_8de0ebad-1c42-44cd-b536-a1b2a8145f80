import { api } from './base'
import { PlanType } from '../store'
import { Target } from './saving-plans-target'

const URL = 'savings-to-crypto-app/saving-plan-settings/'

export enum PurchaseInterval {
  Week = 'W',
  Month = 'M',
  Quarter = 'Q',
}

interface SavingPlanPostProps {
  name: string;
  plan_type: PlanType;
  percentage_value: number;
  ignore_transaction_above: number
  saving_enabled: boolean
  time_horizon: number
}

export interface SavingPlanProps extends SavingPlanPostProps {
  user: string
  name: string
  plan_type: PlanType
  percentage_value: number
  amount_value: number
  ignore_transaction_above: number
  saving_enabled: boolean
  time_horizon: number
  purchase_interval?: PurchaseInterval
  target: Target
}

export const savingPlanSettings = {
  get: () => api.get<SavingPlanProps>(`${URL}`),
  post: (data: SavingPlanProps) => api.post<SavingPlanProps>(`${URL}`, data),
  path: () => api.patch<SavingPlanProps>(`${URL}`),
}
