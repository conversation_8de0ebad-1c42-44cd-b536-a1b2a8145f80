import { api } from './base'

const URL = 'terms-and-conditions/'

export interface Agreement {
  id: number
  label: string
  required: boolean
  name: string
  checked?: boolean
  content?: string
}

export interface TermsOfUseResponse {
  version: string
  agreements: Agreement[]
}

export interface TermsPostData {
  username: string
  agreements: Agreement[]
}

export const termsAndConditions = {
  get: () => api.get<TermsOfUseResponse>(`${URL}`),
  post: ({ username, agreements }: TermsPostData) => {

    const agreementsMap = agreements.filter(term => ({
      id: term.id,
      accepted: term.checked,
    }))

    return api.post(`${URL}`, { username, agreements: agreementsMap })
  },
}
