import axios, { AxiosError } from 'axios'
import { tokenService } from '@services'
import { ITokenResponse } from '@factory/auth'
import * as SecureStore from 'expo-secure-store'
import { SecureStorageKeys } from '@const/secureStorageKeys'

const BASE_URL = process.env.EXPO_PUBLIC_BASE_URL
const API_PREFIX = process.env.EXPO_PUBLIC_API_PREFIX

const NO_AUTH_ENDPOINTS = [
  '/token/',
  '/refresh/',
  '/register/',
  '/confirm-registration-code/',
  '/reset-password/',
  '/reset-password-confirm/',
  '/api/token/refresh/',
]

const DEFAULT_HEADERS = {
  language: 'PL'
}

function createAxiosInstance(baseURL: string, defaultHeaders = DEFAULT_HEADERS) {
  const instance = axios.create({
    baseURL,
    headers: defaultHeaders,
  })

  instance.interceptors.request.use(
    config => {
      const token = tokenService.token
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    },
    error => Promise.reject(error)
  )

  instance.interceptors.response.use(
    response => response,
    async (error: AxiosError) => {
      const originalRequest: any = error.config

      if (
        error.response?.status === 401 &&
        !originalRequest._retry &&
        originalRequest.url &&
        !NO_AUTH_ENDPOINTS.some(endpoint =>
          originalRequest.url.includes(endpoint),
        )
      ) {
        originalRequest._retry = true

        try {
          const refreshedTokenResponse = await refreshToken()
          tokenService.setToken(refreshedTokenResponse)

          originalRequest.headers.Authorization = `Bearer ${refreshedTokenResponse.access}`
          return instance(originalRequest)
        } catch (refreshError) {
          tokenService.clearTokens() // Clear tokens, logout user safely
          return Promise.reject(refreshError)
        }
      }

      return Promise.reject(error)
    }
  )

  return instance
}

async function refreshToken(): Promise<ITokenResponse> {
  const refreshToken = await SecureStore.getItemAsync(SecureStorageKeys.REFRESH_TOKEN)
  if (!refreshToken) {
    throw new Error('No refresh token available')
  }

  const refreshInstance = axios.create()

  const response = await refreshInstance.post<ITokenResponse>(`${BASE_URL}/api/token/refresh/`, {
    refresh: refreshToken,
  })

  return response.data
}

export const api = createAxiosInstance(`${BASE_URL}/${API_PREFIX}`)
