import React from 'react'
import { ScrollView, View, Image, StyleSheet } from 'react-native'
import { Text, List } from 'react-native-paper'
import { LinearGradient } from 'expo-linear-gradient'
import { useRouter } from 'expo-router'
import { theme } from '@theme'
import { useTranslations } from '@hooks'
import UiHeaderBackButton from '@ui/Buttons/UiHeaderBackButton'
import PlanBox from '@ui/box/planBox'

export default function ProfileScreen() {
  const router = useRouter()
  const t = useTranslations()

  return (
    <View style={styles.container}>
      <View style={styles.backgroundContainer}>
        <Image
          source={require('../../assets/test-profile-picture.png')}
          style={styles.backgroundAvatar}
          blurRadius={50}
        />
        <LinearGradient
          colors={['rgba(242, 244, 243, 0)', '#f2f4f3']}
          style={styles.gradientOverlay}
          locations={[0, 0.7]}
        />
      </View>
      <View style={styles.backButtonContainer}>
        <UiHeaderBackButton />
      </View>

      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.profileHeader}>
          <Image
            source={require('../../assets/test-profile-picture.png')}
            style={styles.avatar}
          />
          <Text style={styles.name}>Marek Wiśniewski</Text>
        </View>

        <View style={styles.infoBoxesContainer}>
          <View style={styles.infoBox}>
              <PlanBox />
          </View>
          <View style={styles.infoBox}></View>
        </View>

        <View style={styles.section}>
          <List.Item
            title={t('navigation.myData')}
            left={props => <List.Icon {...props} icon="account-outline" />}
            onPress={() => router.push('(navigation)')}
          />
          <List.Item
            title={t('navigation.verification')}
            left={props => <List.Icon {...props} icon="check-decagram-outline" />}
            right={() => <Text style={styles.verified}>{t('common.verified')}</Text>}
            onPress={() => router.push('(id-verification)')}
          />
          <List.Item
            title={t('navigation.savingPlan')}
            left={props => <List.Icon {...props} icon="piggy-bank-outline" />}
            onPress={() => router.push('(saving-plan)')}
          />
          <List.Item
            title={t('navigation.connectedBankAccounts')}
            left={props => <List.Icon {...props} icon="bank-outline" />}
            onPress={() => router.push('(bank-connection)')}
          />
          <List.Item
            title={t('navigation.cardPaymentConnected')}
            left={props => <List.Icon {...props} icon="credit-card-outline" />}
            onPress={() => router.push('(navigation)')}
          />
          <List.Item
            title={t('navigation.web3wallet')}
            left={props => <List.Icon {...props} icon="wallet-outline" />}
            onPress={() => router.push('(navigation)')}
          />
        </View>

        <View style={styles.section}>
          <List.Item
            title={t('navigation.biometricLogin')}
            left={props => <List.Icon {...props} icon="fingerprint" />}
            onPress={() => router.push('(security)')}
          />
          <List.Item
            title={t('navigation.changePassword')}
            left={props => <List.Icon {...props} icon="lock-reset" />}
            onPress={() => router.push('(security)')}
          />
          <List.Item
            title={t('navigation.retiHowWeSecureYourData')}
            left={props => <List.Icon {...props} icon="shield-check-outline" />}
            onPress={() => router.push('(security)')}
          />
        </View>

        <View style={styles.section}>
          <List.Item
            title={t('navigation.pushNotifications')}
            left={props => <List.Icon {...props} icon="bell-outline" />}
            onPress={() => router.push('(navigation)')} //settings in the future
          />
          <List.Item
            title={t('navigation.language')}
            left={props => <List.Icon {...props} icon="translate" />}
            onPress={() => router.push('(navigation)')} //settings in the future
          />
        </View>

        <View style={styles.section}>
          <List.Item
            title={t('navigation.rulebook')}
            left={props => <List.Icon {...props} icon="file-document-outline" />}
            onPress={() => router.push('(navigation)')} // terms in the future
          />
          <List.Item
            title={t('navigation.reportbug')}
            left={props => <List.Icon {...props} icon="bug-outline" />}
            onPress={() => router.push('(navigation)')} // report bug in the future
          />
          <List.Item
            title={t('navigation.about')}
            left={props => <List.Icon {...props} icon="information-outline" />}
            onPress={() => router.push('(navigation)')} // about in the future
          />
          <List.Item
            title={t('navigation.logout')}
            titleStyle={{ color: theme.colors.error }}
            left={props => (
              <List.Icon {...props} icon="logout" color={theme.colors.error} />
            )}
            onPress={() => {
              router.push('/login')
            }}
          />
        </View>

        <Text style={styles.footer}>Wersja 1.0.0</Text>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "lilyWhite",
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '25%',
  },
  backgroundAvatar: {
    width: '100%',
    height: '100%',
    opacity: 0.5,
  },
  gradientOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: '100%',
  },
  backButtonContainer: {
    position: 'absolute',
    top: 40,
    left: 16,
    zIndex: 1,
  },
  scrollContainer: {
    padding: 16,
    paddingBottom: 32,
    paddingTop: 60,
  },
  profileHeader: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 24,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 100,
    marginBottom: 12,
    borderWidth: 4,
    borderColor: theme.colors.lilyWhite,
  },
  name: {
    fontSize: 24,
    fontWeight: '500',
    color: theme.colors.text,
  },
  infoBoxesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 32,
    gap: 16,
  },
  infoBox: {
    backgroundColor: 'white',
    height: 100,
    flex: 1,
    borderRadius: 16,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    paddingHorizontal: 8,
    marginBottom: 32,
  },
  verified: {
    color: '#24D327',
    backgroundColor: '#24D3271A',
    fontSize: 12,
    marginTop: 10,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 100,
    overflow: 'hidden',
  },
  footer: {
    textAlign: 'center',
    marginTop: 12,
    fontSize: 12,
    color: theme.colors.gray,
  },
})