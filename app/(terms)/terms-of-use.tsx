import { View, Text, StyleSheet, TouchableOpacity } from 'react-native'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import { UiHeader } from '@ui/typography'
import { Card, IconButton,MD3Colors } from 'react-native-paper'
import { useCallback, useEffect, useMemo, useState } from 'react'
import {
  errorHandlerService,
  ErrorKey,
  registrationService,
  termsService,
} from '@services'
import SVGTerm from '../../assets/zgody.svg'
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'
import UiDividerWithLabel from '@ui/divider/UiDividerWithLabel'
import { UiCheckbox } from '@ui/UiCheckbox/UiCheckbox'
import { useRouter } from 'expo-router'
import useRegistrationStore from '@store/registration.store'
import { theme } from '@theme'
import { catchError, of, switchMap } from 'rxjs'
import { Agreement } from '@factory/terms'

const Row = () => {
  const router = useRouter()

  const onPress = useCallback((agreement: Agreement) => {
    router.push({ pathname: '/term', params: { agreementId: agreement.id } })
  }, [])

  const { agreements } = useRegistrationStore(state => ({
    agreements: state.agreementsArray,
  }))

  return (
    <View style={[styles.row]}>
      {agreements?.map(agreement => (
        <View key={`agreement-row-${agreement.id}-${agreement.name}`}>
          <View style={styles.rowElement}>
            <TouchableOpacity
              style={styles.rowElement}
              onPress={() => onPress(agreement)}
            >
              <SVGTerm />
              <Text style={styles.label}>{agreement.name}</Text>
            </TouchableOpacity>
            <View style={styles.rowElementIconContainer}>
              <IconButton
                size={32}
                icon={'chevron-right'}
                color={MD3Colors.neutral30}
                onPress={() => onPress(agreement)}
                style={styles.chevron}
              />
            </View>
          </View>
        </View>
      ))}
    </View>
  )
}

export default () => {
  const router = useRouter()
  const [loading, setIsLoading] = useState(false)

  const {
    username,
    password,
    setAgreements,
    setAgreement,
    setAllChecked,
    agreementsArray,
  } = useRegistrationStore(state => ({
    username: state.username,
    password: state.password,
    setAgreements: state.setAgreements,
    setAgreement: state.setAgreement,
    setAllChecked: state.setAllChecked,
    agreementsArray: state.agreementsArray,
  }))

  const setAllCheckedFields = useCallback(() => {
    setAllChecked()
  }, [setAllChecked])

  const allChecked = useMemo(() => {
    if (agreementsArray) {
      return agreementsArray
        ?.filter(item => item.required)
        .every(item => item.checked)
    } else {
      return false
    }
  }, [agreementsArray])

  useEffect(() => {
    termsService.getTerms().subscribe({
      next: data => {
        if (data?.agreements) {
          setAgreements(data.agreements)
        }
      },
      error: e => {
        console.warn(e)
      },
    })
  }, [])

  const registerAccount = () => {
    errorHandlerService.clearAllErrors()
    setIsLoading(true)
    registrationService
      .createAccount({
        username,
        password,
      })
      .pipe(
        switchMap(value => {
          console.log('value', value);
          const { agreementsArray, username } = useRegistrationStore?.getState()
          termsService.postTerms({ agreements: agreementsArray || [], username })
          return of(value)
        }),
      )
      .subscribe({
        next: () => {
          setIsLoading(false)
          registrationService
            .resendActivationCode(username)
            .pipe(catchError((error) => {
              console.log('catch error resend activation code', error?.response?.data);
              return of(false)
            }))
            .subscribe(() => {
              setTimeout(() => {
                router.replace({
                  pathname: '(confirmation)',
                  params: { username },
                })
              }, 500)
            })
        },
        error: error => {
          console.log(error.response?.data);
          const errorMessage: string | undefined = Object.values(
            error?.response?.data,
          )?.[0]?.toString()

          setIsLoading(false)
          if (errorMessage) {
            errorHandlerService.add(
              ErrorKey.RegistrationError,
              errorMessage,
              error?.status,
              false,
            )
          } else {
            errorHandlerService.add(
              ErrorKey.RegistrationError,
              'messages.registrationError',
              error?.status,
            )
          }

          setTimeout(() => {
            router.replace({
              pathname: '/init-screen',
              params: { username },
            })
          }, 3500)
        },
      })
  }

  return (
    <UiScreenContainer styles={{ backgroundColor: theme.colors.lilyWhite }}>
      <UiHeader
        translatedText="termsOfUse.title"
        style={{
          textAlign: 'left',
        }}
      />
      <Card style={styles.card} elevation={1}>
        <Card.Content>
          <Row />
        </Card.Content>
      </Card>
      <View style={styles.container}>
        <View>
          <UiCheckbox
            label="Zaznacz wszystkie zgody"
            initialChecked={false}
            onPress={setAllCheckedFields}
          />
        </View>
        <View>
          <UiDividerWithLabel />
        </View>
        {agreementsArray && (
          <View style={styles.subCheckBox}>
            {agreementsArray?.map(agreement => {
              return (
                <UiCheckbox
                  key={`${agreement.id}-${agreement.name}`}
                  required={agreement.required}
                  label={`${agreement.label}`}
                  initialChecked={agreement.checked || false}
                  onPress={val => {
                    setAgreement(agreement.name, val)
                  }}
                />
              )
            })}
          </View>
        )}
      </View>
      <View style={styles.buttonContainer}>
        <UiPrimaryButton
          disabled={!allChecked}
          loading={loading}
          translationTitle="signUp.buttons.signUp.label"
          mode="contained"
          onPress={registerAccount}
        />
      </View>
    </UiScreenContainer>
  )
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: theme.colors.background,
  },
  container: {
    marginTop: 30,
    flex: 3,
  },
  buttonContainer: {
    flex: 1,
    marginBottom: 5,
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    paddingBottom: 30,
  },
  row: {
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  rowElement: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  rowElementIconContainer: {
    flex: 1,
    alignItems: 'flex-end'
  },
  chevron:{
    padding:0,
    margin:0,
  },
  label: {
    marginLeft: 10,
  },
  margin: {
    marginBottom: 16,
  },
  subCheckBox: {
    marginLeft: 16,
  },
})
