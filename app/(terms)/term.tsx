import { ScrollView, StyleSheet, View } from 'react-native';
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton';
import { UiScreenContainer } from '@ui/containers/UiScreenContainer';
import { useGlobalSearchParams, useRouter } from 'expo-router';
import { useCallback } from 'react';
import { Text } from 'react-native-paper';
import { theme } from '@theme';
import useRegistrationStore from '@store/registration.store';

export default () => {
  const router = useRouter();

  const onBack = useCallback(() => {
    router.back();
  }, []);

  const params = useGlobalSearchParams();

  const { agreementsArray } = useRegistrationStore(state => ({
    agreementsArray: state.agreementsArray,
  }));

  const agreement = agreementsArray?.find(
    element => element.id === parseInt(params.agreementId as string, 10),
  );

  return (
    <UiScreenContainer styles={{ backgroundColor: theme.colors.lilyWhite }}>
      <View>
        <Text variant="titleLarge" style={style.title}>
          {agreement?.name}
        </Text>
      </View>
      <View style={style.container}>
        <ScrollView style={style.scrollView}>
          <Text variant="bodyLarge">{agreement?.content}</Text>
        </ScrollView>
      </View>
      <View style={style.buttonContainer}>
        <UiPrimaryButton
          onPress={onBack}
          translationTitle="common.back"
          mode="contained"
        />
      </View>
    </UiScreenContainer>
  );
};

const style = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
  },
  scrollView: {
    flex: 1,
    marginBottom: 60,
  },
  buttonContainer: {
    marginBottom: 30,
  },
  title: {
    marginTop: 25,
    fontSize: 32,
    lineHeight: 37,
    marginBottom: 40,
    textAlign: 'left',
    color: theme.colors.text,
  },
});
