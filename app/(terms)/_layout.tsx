import { Stack } from 'expo-router'
import UiHeaderBackButton from '@ui/Buttons/UiHeaderBackButton'
import { HeaderLogo } from '@ui/header/headerLogo/headerLogo'
import { theme } from '@theme'


export default () => {
  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
        headerLeft: () => <UiHeaderBackButton />,
        title: '',
        headerTitle: () => <HeaderLogo />,
        headerStyle:{backgroundColor: theme.colors.lilyWhite}
      }}
    >
      <Stack.Screen name="index" />
      <Stack.Screen name="terms-of-use" />
      <Stack.Screen name="term" />
    </Stack>
  )
}
