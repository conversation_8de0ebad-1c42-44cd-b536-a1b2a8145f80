import { Stack, useRootNavigationState } from 'expo-router'
import { PaperProvider } from 'react-native-paper'
import * as SplashScreen from 'expo-splash-screen'
import { MainContainer } from '@components'
import { theme } from '@theme'
import { UiModal } from '@ui/UiModal'
import { useEffect, useState } from 'react'
import { useOnAuthorizationChange } from '@hooks'
import useOnAuthorizationPending from '../hooks/useAuthorizationPending'

const RootLayout = () => {

  const [isLoaded, setIsLoaded] = useState(false)
  useEffect(() => {
    setIsLoaded(true);
  }, [])

  useOnAuthorizationChange(isLoaded)
  useOnAuthorizationPending(isLoaded)

  return (
    <PaperProvider theme={theme}>
      <UiModal />
      <MainContainer>
        <Stack
          screenOptions={{
            headerShown: false,
          }}
        >
          <Stack.Screen name="index" />
          <Stack.Screen name="init-screen" />
          <Stack.Screen name="(registration)" />
          <Stack.Screen name="(authorization)" />
          <Stack.Screen
            name="transition-screen"
            options={{
              presentation: 'transparentModal',
            }}
          />
        </Stack>
      </MainContainer>
    </PaperProvider>
  )
}

export default RootLayout
