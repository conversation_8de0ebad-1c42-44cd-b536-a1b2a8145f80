import { UiHeader } from '@ui/typography'
import { EmailForm, SignWithGoogleButton } from '@components'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import { View } from 'react-native'
import UiDivider<PERSON>ithLabel from '@ui/divider/UiDividerWithLabel'
import React from 'react'

export default () => {
  return (
    <UiScreenContainer>
      <UiHeader
        translatedText="common.signIn"
        style={{
          textAlign: 'left',
        }}
      />
      <EmailForm redirectPath="/sign-in" />
      <View>
        <UiDividerWithLabel label="common.or" />
      </View>
      <SignWithGoogleButton/>
    </UiScreenContainer>
  )
}
