import { Stack, useRouter } from 'expo-router'
import UiHeaderBackButton from '@ui/Buttons/UiHeaderBackButton'

const AuthorizationLayout = () => {
  const router = useRouter()

  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
      }}
    >
      <Stack.Screen
        name="sign-in"
        options={{
          title: '',
          headerLeft: () => <UiHeaderBackButton overrideOnPress={()=> {
            router.push('/init-screen')
          }}/>,
        }}
      />
      <Stack.Screen
        name="index"
        options={{
          title: '',
          headerLeft: () => <UiHeaderBackButton />,
        }}
      />
    </Stack>
  )
}

export default AuthorizationLayout
