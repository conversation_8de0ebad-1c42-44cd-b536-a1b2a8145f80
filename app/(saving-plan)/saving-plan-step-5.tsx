import React, { useCallback } from 'react'
import { StyleSheet } from 'react-native'
import { useRouter } from 'expo-router'
import { DEFAULT_SAVING_PLAN__TOTAL_STEPS } from '@const/savingPlanSteps'
import { useSetProgressPosition } from '@hooks'
import { theme } from '@theme'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import { UiAccordionSavingPlan, UiPrimaryButton } from '@ui/index'
import { UiHeader } from '@ui/typography'
import SvgDollar from '../../assets/dollar.svg'
import SvgCoins from '../../assets/coins.svg'
import useSavingPlanStore, { PlanType } from '@store/saving-plan-store'

const plans = [
  {
    key: PlanType.Percentage,
    titleKey: 'savingPlan.plans.adjusted.title',
    descriptionKey: 'savingPlan.plans.adjusted.description',
    tagsKey: 'savingPlan.plans.adjusted.tags',
    alert: {
      message: 'savingPlan.plans.adjusted.message',
      type: 'success' as const,
    },
  },
  {
    key: PlanType.ReccuringPurchase,
    titleKey: 'savingPlan.plans.simple.title',
    descriptionKey: 'savingPlan.plans.simple.description',
    tagsKey: 'savingPlan.plans.simple.tags',
    alert: {
      message: 'savingPlan.plans.simple.message',
      type: 'warning' as const,
    },
  },
]

export default () => {
  const router = useRouter()

  const { selectedPlan, setSelectedPlan } = useSavingPlanStore(state => ({
    selectedPlan: state.selectedPlan,
    setSelectedPlan: state.setSelectedPlan,
  }))

  useSetProgressPosition(4, DEFAULT_SAVING_PLAN__TOTAL_STEPS)

  const onPress = useCallback(() => {
    if (selectedPlan === PlanType.ReccuringPurchase) {
      router.push('/simple/')
    } else {
      router.push('/adjusted/')
    }
  }, [selectedPlan])

  const onSelect = useCallback((planName: PlanType) => {
    setSelectedPlan(planName)
  }, [])

  return (
    <UiScreenContainer styles={styles.screenContainer}>
      <UiHeader
        translatedText="savingPlan.plans.title"
        style={styles.header}
      />
      {plans.map(plan => (
        <UiAccordionSavingPlan
          key={plan.key}
          id={plan.key}
          icon={plan.key === PlanType.Amount ? <SvgDollar /> : <SvgCoins />}
          titleKey={plan.titleKey}
          descriptionKey={plan.descriptionKey}
          tagsKey={plan.tagsKey}
          alert={plan.alert}
          isSelected={selectedPlan === plan.key}
          onSelect={onSelect}
          isTabSectionVisible
          isAlertVisible
        />
      ))}
      <UiPrimaryButton
        onPress={onPress}
        disabled={!selectedPlan}
        translationTitle="common.continue"
        mode="contained"
        containerStyle={styles.buttonContainer}
      />
    </UiScreenContainer>
  )
}

const styles = StyleSheet.create({
  screenContainer: {
    backgroundColor: theme.colors.lilyWhite,
  },
  header: {
    paddingRight: 20,
    textAlign: 'left',
    marginBottom: 20,
  },
  buttonContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    width: '100%',
    marginBottom: 30,
  },
})
