import { StyleSheet, View } from 'react-native'
import { theme } from '@theme'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import Animated, { BounceInDown, FadeIn } from 'react-native-reanimated'
import { Text } from 'react-native-paper'
import useSavingPlanStore, { PlanType } from '@store/saving-plan-store'
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'
import { usePluralUnit, useTranslations } from '@hooks'
import SvgHouse from '../../../assets/modern-house.svg'
import SvgHouse1 from '../../../assets/scandinavian-house.svg'
import SvgCar from '../../../assets/car-rental.svg'
import SvgFinance from '../../../assets/finance.svg'
import { useCallback } from 'react'
import { useRouter } from 'expo-router'
import savingPlanService from '../../../services/saving-plan/saving-plan.service'

const Label = ({
  translatedText,
  value = '',
}: {
  translatedText?: string
  value?: string
}) => {
  const t = useTranslations()
  return (
    <View>
      <Text variant="titleLarge" style={styles.label}>
        {translatedText ? t(translatedText) : value}
      </Text>
    </View>
  )
}

const LabelContent = ({ value }: { value: string | number }) => {
  return (
    <View>
      <Text variant="headlineMedium" style={styles.labelContent}>
        {value}
      </Text>
    </View>
  )
}

const TranslatedLabelContent = ({
  translatedText,
}: {
  translatedText: string
}) => {
  const t = useTranslations()
  return <LabelContent value={t(translatedText)} />
}

const SelectedTargetIcon = ({ target }: { target: string }) => {
  switch (target) {
    case 'Auto':
      return <SvgCar width={40} height={40} />
    case 'Nowy dom':
      return <SvgHouse1 width={40} height={40} />
    case 'Finanse':
      return <SvgFinance width={40} height={40} />
    default:
      return <SvgHouse width={40} height={40} />
  }
}

export default () => {
  const router = useRouter()
  const {
    planName,
    timeHorizon,
    selectedPlan,
    adjustedPlanPercentage,
    selectedTarget,
    ignoreTransactionsAbove,
    fixedPlanValue,
    savingAmountAndFrequency,
    adjustedPlanOption,
  } = useSavingPlanStore(state => ({
    planName: state.planName,
    timeHorizon: state.timeHorizon,
    selectedPlan: state.selectedPlan,
    adjustedPlanPercentage: state.adjustedPlanPercentage,
    selectedTarget: state.selectedTarget,
    ignoreTransactionsAbove: state.ignoreTransactionsAbove,
    fixedPlanValue: state.fixedPlanValue,
    savingAmountAndFrequency: state.savingAmountAndFrequency,
    adjustedPlanOption: state.adjustedPlanOption,
  }))

  const onChangePress = useCallback(() => {
    router.replace('(saving-plan)')
  }, [])

  const createPlanPress = useCallback(() => {
    const storedData = useSavingPlanStore?.getState();
    savingPlanService.saveUserPlan(storedData).subscribe({
      next: (value)=> console.log(value),
      complete:()=> {
        router.push('/saving-plan-created')
      },
      error:(e)=> console.log('error', e.response.data),
    })
    //
  }, [])

  return (
    <UiScreenContainer styles={styles.screenContainer}>
      <Animated.View style={styles.header} entering={FadeIn.delay(500)}>
        {selectedTarget && (
          <View style={styles.selectedIconContainer}>
            <SelectedTargetIcon target={selectedTarget.icon} />
          </View>
        )}
        <Text variant="headlineLarge" style={styles.headerText}>
          {planName}
        </Text>
        <Text variant="headlineSmall" style={styles.headerTextSmall}>
          {selectedTarget?.icon_label}
        </Text>
      </Animated.View>
      <Animated.View style={styles.card} entering={BounceInDown.delay(350)}>
        <View>
          <View style={styles.labelContainer}>
            <Label translatedText="savingPlan.summary.content.timeHorizon" />
            <LabelContent value={timeHorizon} />
          </View>
          <View style={styles.labelContainer}>
            <Label translatedText="savingPlan.summary.content.savingType" />
            {selectedPlan === PlanType.Amount || selectedPlan === PlanType.Percentage && (
                <TranslatedLabelContent translatedText="savingPlan.plans.adjusted.title" />
              )}
            {selectedPlan === PlanType.ReccuringPurchase && (
              <TranslatedLabelContent translatedText="savingPlan.plans.simple.title" />
            )}
          </View>
          {selectedPlan === PlanType.Amount || selectedPlan === PlanType.Percentage && (
            <>
              <View style={styles.labelContainer}>
                {adjustedPlanOption === PlanType.Amount && (
                  <>
                    <Label translatedText="savingPlan.adjustedPlan.fixedPlan.heading" />
                    <View style={styles.ignoreTransactionContainer}>
                      <Label translatedText="savingPlan.summary.content.savingFixedValue" />
                      <LabelContent value={fixedPlanValue} />
                      <Label translatedText="units.money" />
                    </View>
                  </>
                )}
                {adjustedPlanOption === PlanType.Percentage && (
                  <>
                    <Label translatedText="savingPlan.adjustedPlan.percentagePlan.heading" />
                    <View style={styles.ignoreTransactionContainer}>
                      <Label translatedText="savingPlan.summary.content.savingPercentage" />
                      <LabelContent value={adjustedPlanPercentage} />
                      <Label translatedText="units.percent" />
                    </View>
                  </>
                )}
              </View>
            </>
          )}
          {selectedPlan === PlanType.ReccuringPurchase && (
            <View style={styles.labelContainer}>
              <Label translatedText="savingPlan.summary.content.savingFixedValue" />
              <View style={styles.ignoreTransactionContainer}>
                <LabelContent value={savingAmountAndFrequency.amount} />
                <Label translatedText="units.money" />
                <Label value=" / " />
                <Label
                  translatedText={`savingPlan.amountStep.frequencies.${savingAmountAndFrequency.frequency}`}
                />
              </View>
            </View>
          )}
          {ignoreTransactionsAbove > 0 && (
            <View style={styles.labelContainer}>
              <Label translatedText="savingPlan.summary.content.ignoreTransaction" />
              <View style={styles.ignoreTransactionContainer}>
                <Label translatedText="savingPlan.summary.content.ignoreTransactionPrefix" />
                <LabelContent value={ignoreTransactionsAbove} />
                <Label translatedText="units.money" />
              </View>
            </View>
          )}
        </View>
        <View style={styles.buttonsContainer}>
          <UiPrimaryButton
            containerStyle={styles.button}
            translationTitle="savingPlan.summary.buttons.changePlan"
            mode="outlined"
            onPress={onChangePress}
          />
          <UiPrimaryButton
            containerStyle={styles.button}
            translationTitle="savingPlan.summary.buttons.createPlan"
            mode="contained"
            onPress={createPlanPress}
          />
        </View>
      </Animated.View>
    </UiScreenContainer>
  )
}

export const InfoSavedPlanContainer = () => {

  const {
    timeHorizon,
    selectedPlan,
    adjustedPlanPercentage,
    ignoreTransactionsAbove,
    fixedPlanValue,
    savingAmountAndFrequency,
    adjustedPlanOption,
  } = useSavingPlanStore(state => ({
    planName: state.planName,
    timeHorizon: state.timeHorizon,
    selectedPlan: state.selectedPlan,
    adjustedPlanPercentage: state.adjustedPlanPercentage,
    selectedTarget: state.selectedTarget,
    ignoreTransactionsAbove: state.ignoreTransactionsAbove,
    fixedPlanValue: state.fixedPlanValue,
    savingAmountAndFrequency: state.savingAmountAndFrequency,
    adjustedPlanOption: state.adjustedPlanOption,
  }))

  const t = useTranslations()

  const getPluralUnit = usePluralUnit()
  const unit = t(getPluralUnit(timeHorizon, 'savingPlan.timeHorizonStep.unit'))
  return (
      <View>
        <View style={styles.labelContainer}>
          <Label translatedText="savingPlan.summary.content.timeHorizon" />
          <LabelContent value={`${timeHorizon} ${unit}`} />
        </View>
        <View style={styles.labelContainer}>
          <Label translatedText="savingPlan.summary.content.savingType" />
          {selectedPlan === PlanType.Amount || selectedPlan === PlanType.Percentage && (
            <TranslatedLabelContent translatedText="savingPlan.plans.adjusted.title" />
          )}
          {selectedPlan === PlanType.ReccuringPurchase && (
            <TranslatedLabelContent translatedText="savingPlan.plans.simple.title" />
          )}
        </View>
        {selectedPlan === PlanType.Amount || selectedPlan === PlanType.Percentage && (
          <>
            <View style={styles.labelContainer}>
              {adjustedPlanOption === PlanType.Amount && (
                <>
                  <Label translatedText="savingPlan.adjustedPlan.fixedPlan.heading" />
                  <View style={styles.ignoreTransactionContainer}>
                    <Label translatedText="savingPlan.summary.content.savingFixedValue" />
                    <LabelContent value={fixedPlanValue} />
                    <Label translatedText="units.money" />
                  </View>
                </>
              )}
              {adjustedPlanOption === PlanType.Percentage && (
                <>
                  <Label translatedText="savingPlan.adjustedPlan.percentagePlan.heading" />
                  <View style={styles.ignoreTransactionContainer}>
                    <Label translatedText="savingPlan.summary.content.savingPercentage" />
                    <LabelContent value={adjustedPlanPercentage} />
                    <Label translatedText="units.percent" />
                  </View>
                </>
              )}
            </View>
          </>
        )}
        {selectedPlan === PlanType.ReccuringPurchase && (
          <View style={styles.labelContainer}>
            <Label translatedText="savingPlan.summary.content.savingFixedValue" />
            <View style={styles.ignoreTransactionContainer}>
              <LabelContent value={savingAmountAndFrequency.amount} />
              <Label translatedText="units.money" />
              <Label value=" / " />
              <Label
                translatedText={`savingPlan.amountStep.frequencies.${savingAmountAndFrequency.frequency}`}
              />
            </View>
          </View>
        )}
        {ignoreTransactionsAbove > 0 && (
          <View style={styles.labelContainer}>
            <Label translatedText="savingPlan.summary.content.ignoreTransaction" />
            <View style={styles.ignoreTransactionContainer}>
              <Label translatedText="savingPlan.summary.content.ignoreTransactionPrefix" />
              <LabelContent value={ignoreTransactionsAbove} />
              <Label translatedText="units.money" />
            </View>
          </View>
        )}
      </View>
  )
}

const styles = StyleSheet.create({
  screenContainer: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 0,
    flex: 1,
  },
  buttonsContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
  },
  ignoreTransactionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  selectedIconContainer: {
    width: 72,
    height: 72,
    borderRadius: 100,
    backgroundColor: theme.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
  },
  button: {
    width: '45%',
  },
  header: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 40,
  },
  headerText: {
    color: theme.colors.lilyWhite,
    textAlign: 'center',
    fontSize: 32,
    fontWeight: '500',
  },
  headerTextSmall: {
    color: theme.colors.pastelAqua,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '400',
  },
  card: {
    flex: 2,
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    width: '100%',
    backgroundColor: theme.colors.background,
    paddingVertical: 30,
    paddingHorizontal: 24,
  },
  label: {
    color: theme.colors.gray,
    fontSize: 18,
    fontWeight: 400,
  },
  labelContent: {
    fontSize: 24,
  },
  labelContainer: {
    marginBottom: 15,
  },
})
