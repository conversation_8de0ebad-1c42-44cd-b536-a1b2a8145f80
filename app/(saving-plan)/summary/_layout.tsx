import { useCallback } from 'react'
import { Stack } from 'expo-router'
import { authorizationService } from '@services'
import { theme } from '@theme'
import UiHeaderBackButton from '@ui/Buttons/UiHeaderBackButton'
import UiHeaderCloseButton from '@ui/Buttons/UiHeaderCloseButton'
import { HeaderProgress } from '@ui/header/headerProgress/headerProgress'
import useProgressStore from '@store/progress.store'

const SummaryLayout = () => {

  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          headerShown: false,
        }}
      />
    </Stack>
  )
}

export default SummaryLayout
