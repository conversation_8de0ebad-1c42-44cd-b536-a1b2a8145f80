import React, { useCallback } from 'react'
import { StyleSheet, View } from 'react-native'
import { useRouter } from 'expo-router'
import { DEFAULT_SAVING_PLAN__TOTAL_STEPS } from '@const/savingPlanSteps'
import { useSetProgressPosition } from '@hooks'
import { theme } from '@theme'
import { UiAlert } from '@ui/alert/UiAlert'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import { UiPrimaryButton } from '@ui/index'
import { UiHeader } from '@ui/typography'
import { TextInputField } from '@ui/TextInputField/TextInputField'
import useSavingPlanStore from '@store/saving-plan-store'

export default () => {
  const { planName, setPlanName } = useSavingPlanStore(state => ({
    planName: state.planName,
    setPlanName: state.setPlanName,
  }))
  const router = useRouter()

  useSetProgressPosition(2, DEFAULT_SAVING_PLAN__TOTAL_STEPS)

  const onPress = useCallback(() => {
    router.push('/saving-plan-step-4')
  }, [])

  return (
    <UiScreenContainer styles={styles.screenContainer}>
      <UiHeader
        translatedText="savingPlan.nameStep.title"
        style={styles.header}
      />
      <UiAlert message="savingPlan.nameStep.info" type="info" />
      <View style={styles.inputContainer}>
        <TextInputField
          value={planName}
          onChangeText={setPlanName}
          labelTranslation="savingPlan.nameStep.inputLabel"
        />
      </View>
      <UiPrimaryButton
        disabled={!planName}
        onPress={onPress}
        translationTitle="common.continue"
        mode="contained"
        containerStyle={styles.buttonContainer}
      />
    </UiScreenContainer>
  )
}

const styles = StyleSheet.create({
  screenContainer: {
    backgroundColor: theme.colors.lilyWhite,
  },
  header: {
    paddingRight: 20,
    textAlign: 'left',
    marginBottom: 20,
  },
  inputContainer: {
    marginTop: 40,
  },
  buttonContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    width: '100%',
    marginBottom: 30,
  },
})
