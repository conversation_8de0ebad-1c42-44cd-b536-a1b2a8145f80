import React, { useCallback } from 'react'
import { StyleSheet, View } from 'react-native'
import { useRouter } from 'expo-router'
import { DEFAULT_SAVING_PLAN__TOTAL_STEPS } from '@const/savingPlanSteps'
import { useSetProgressPosition, usePluralUnit } from '@hooks'
import { theme } from '@theme'
import { UiAlert } from '@ui/alert/UiAlert'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import { UiPrimaryButton, UiStepper } from '@ui/index'
import { UiHeader } from '@ui/typography'
import useSavingPlanStore from '@store/saving-plan-store'

export default () => {
  const { timeHorizon, setTimeHorizon } = useSavingPlanStore(state => ({
    timeHorizon: state.timeHorizon,
    setTimeHorizon: state.setTimeHorizon,
  }))
  const router = useRouter()
  const getPluralUnit = usePluralUnit()

  useSetProgressPosition(3, DEFAULT_SAVING_PLAN__TOTAL_STEPS)

  const onPress = useCallback(() => {
    router.push('/saving-plan-step-5')
  }, [])

  return (
    <UiScreenContainer styles={styles.screenContainer}>
      <UiHeader
        translatedText="savingPlan.timeHorizonStep.title"
        style={styles.header}
      />
      <UiAlert message="savingPlan.timeHorizonStep.info" type="info" />
      <View style={styles.stepperContainer}>
        <UiStepper
          value={timeHorizon}
          onChange={setTimeHorizon}
          min={1}
          max={100}
          step={1}
          placeholder="1"
          unit={getPluralUnit(timeHorizon, 'savingPlan.timeHorizonStep.unit')}
        />
      </View>
      <UiPrimaryButton
        onPress={onPress}
        translationTitle="common.continue"
        mode="contained"
        containerStyle={styles.buttonContainer}
      />
    </UiScreenContainer>
  )
}

const styles = StyleSheet.create({
  screenContainer: {
    backgroundColor: theme.colors.lilyWhite,
  },
  stepperContainer: {
    marginTop: 40,
  },
  header: {
    paddingRight: 20,
    textAlign: 'left',
    marginBottom: 20,
  },
  buttonContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    width: '100%',
    marginBottom: 30,
  },
})
