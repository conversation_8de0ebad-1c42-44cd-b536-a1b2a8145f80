import React, { useCallback } from 'react'
import { ScrollView, StyleSheet, View } from 'react-native'
import { useRouter } from 'expo-router'
import { DEFAULT_SAVING_PLAN__TOTAL_STEPS } from '@const/savingPlanSteps'
import { useSetProgressPosition } from '@hooks'
import { theme } from '@theme'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import {
  UiAccordion,
  UiAlert,
  UiPrimaryButton,
  UiStepper,
  UiText,
} from '@ui/index'
import { UiHeader } from '@ui/typography'
import SvgPigCoin from '../../../assets/pig-coin.svg'
import SvgPigBanknote from '../../../assets/pig-banknote.svg'
import useSavingPlanStore, { PlanType } from '@store/saving-plan-store'

const WARNING_ADJUSTED_PLAN_PERCENTAGE_VALUE = 8

const AdjustedPlanPercentage = () => {
  const { adjustedPlanPercentage, setAdjustedPlanPercentage } =
    useSavingPlanStore(state => ({
      adjustedPlanPercentage: state.adjustedPlanPercentage,
      setAdjustedPlanPercentage: state.setAdjustedPlanPercentage,
    }))

  return (
    <View>
      <UiText
        translatedText="savingPlan.adjustedPlan.percentagePlan.description"
        variant="bodyLarge"
      />
      <UiText
        style={{ marginTop: 10 }}
        translatedText="savingPlan.adjustedPlan.percentagePlan.recommendation"
        variant="bodyLarge"
      />
      <UiStepper
        value={adjustedPlanPercentage}
        onChange={percentage => setAdjustedPlanPercentage(percentage as number)}
        min={2}
        max={100}
        step={1}
        placeholder="2"
        unit="units.percent"
      />
      {adjustedPlanPercentage >= WARNING_ADJUSTED_PLAN_PERCENTAGE_VALUE && (
        <View style={{ marginTop: 10 }}>
          <UiAlert
            message="savingPlan.adjustedPlan.percentagePlan.alert"
            type="warning"
          />
        </View>
      )}
    </View>
  )
}

const AdjustedPlanFixedAmount = () => {
  const { setFixedPlanValue, fixedPlanValue } = useSavingPlanStore(state => ({
    fixedPlanValue: state.fixedPlanValue,
    setFixedPlanValue: state.setFixedPlanValue,
  }))

  return (
    <View>
      <UiText
        translatedText="savingPlan.adjustedPlan.fixedPlan.description"
        variant="bodyLarge"
      />
      <UiText
        style={{ marginTop: 10 }}
        translatedText="savingPlan.adjustedPlan.fixedPlan.recommendation"
        variant="bodyLarge"
      />
      <UiStepper
        value={fixedPlanValue}
        onChange={value => setFixedPlanValue(value as number)}
        min={1}
        max={100}
        step={5}
        placeholder="0"
        unit="savingPlan.amountStep.unit"
      />
    </View>
  )
}

const adjustedPlans = [
  {
    key: PlanType.Percentage,
    titleKey: 'savingPlan.adjustedPlan.percentagePlan.heading',
    icon: <SvgPigCoin />,
    component: <AdjustedPlanPercentage />,
  },
  {
    key: PlanType.Amount,
    titleKey: 'savingPlan.adjustedPlan.fixedPlan.heading',
    icon: <SvgPigBanknote />,
    component: <AdjustedPlanFixedAmount />,
  },
]

export default () => {
  const router = useRouter()

  const { adjustedPlanOption, setAdjustedPlanOption } = useSavingPlanStore(
    state => ({
      adjustedPlanOption: state.adjustedPlanOption,
      setAdjustedPlanOption: state.setAdjustedPlanOption,
    }),
  )

  useSetProgressPosition(4, DEFAULT_SAVING_PLAN__TOTAL_STEPS)

  const onPress = useCallback(() => {
    router.push('/adjusted/adjusted-ignore-transactions')
  }, [])

  const onSelect = useCallback((planName: PlanType) => {
    setAdjustedPlanOption(planName)
  }, [])

  return (
    <UiScreenContainer styles={styles.screenContainer}>
      <UiHeader
        translatedText="savingPlan.adjustedPlan.title"
        style={styles.header}
      />
      <ScrollView showsVerticalScrollIndicator={false}>
        {adjustedPlans.map(plan => (
          <UiAccordion
            key={plan.key}
            id={plan.key}
            icon={plan.icon}
            titleKey={plan.titleKey}
            isSelected={adjustedPlanOption === plan.key}
            onSelect={onSelect}
            children={plan.component}
          />
        ))}

        <UiPrimaryButton
          onPress={onPress}
          disabled={!adjustedPlanOption}
          translationTitle="common.continue"
          mode="contained"
          containerStyle={styles.buttonContainer}
        />
      </ScrollView>
    </UiScreenContainer>
  )
}

const styles = StyleSheet.create({
  screenContainer: {
    backgroundColor: theme.colors.lilyWhite,
  },
  header: {
    paddingRight: 20,
    textAlign: 'left',
    marginBottom: 20,
  },
  buttonContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    width: '100%',
    marginBottom: 30,
  },
})
