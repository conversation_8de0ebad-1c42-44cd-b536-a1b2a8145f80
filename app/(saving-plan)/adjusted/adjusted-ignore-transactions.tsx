import { StyleSheet, View } from 'react-native'
import { useRouter } from 'expo-router'
import { DEFAULT_SAVING_PLAN__TOTAL_STEPS } from '@const/savingPlanSteps'
import { useSetProgressPosition } from '@hooks'
import { theme } from '@theme'
import useSavingPlanStore from '@store/saving-plan-store'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import { UiAlert, UiPrimaryButton, UiStepper } from '@ui/index'
import { UiHeader } from '@ui/typography'
import { UiTitle } from '@ui/typography/UiTitle'
import { useCallback } from 'react'

export default () => {
  const router = useRouter()
  const { ignoreTransactionsAbove, setIgnoreTransactionsAbove } =
    useSavingPlanStore(state => ({
      ignoreTransactionsAbove: state.ignoreTransactionsAbove,
      setIgnoreTransactionsAbove: state.setIgnoreTransactionsAbove,
    }))
  useSetProgressPosition(9, DEFAULT_SAVING_PLAN__TOTAL_STEPS)

  const onPress = useCallback(() => {
    router.push('/summary')
  }, [])

  return (
    <UiScreenContainer styles={styles.screenContainer}>
      <UiHeader
        translatedText="savingPlan.ignoreTransactionsStep.title"
        style={styles.header}
      />
      <UiTitle
        translatedText="savingPlan.ignoreTransactionsStep.inputLabel"
        style={styles.amountLabel}
      />
      <View style={styles.stepperContainer}>
        <UiStepper
          value={ignoreTransactionsAbove}
          onChange={setIgnoreTransactionsAbove}
          min={0}
          max={2000}
          step={50}
          placeholder="0"
          unit="savingPlan.ignoreTransactionsStep.unit"
        />
      </View>
      <UiAlert
        message="savingPlan.ignoreTransactionsStep.message"
        type="warning"
      />
      <UiPrimaryButton
        onPress={onPress}
        translationTitle="common.continue"
        mode="contained"
        containerStyle={styles.buttonContainer}
      />
    </UiScreenContainer>
  )
}

const styles = StyleSheet.create({
  screenContainer: {
    backgroundColor: theme.colors.lilyWhite,
  },
  header: {
    paddingRight: 20,
    textAlign: 'left',
    marginBottom: 0,
  },
  stepperContainer: {
    marginBottom: 16,
  },
  amountLabel: {
    fontSize: 14,
    fontWeight: 400,
    color: theme.colors.mineralGreen,
    marginBottom: 4,
  },
  buttonContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    width: '100%',
    marginBottom: 30,
  },
})
