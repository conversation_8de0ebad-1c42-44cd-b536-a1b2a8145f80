import React, { useCallback } from 'react'
import {
  Dimensions,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native'
import { useRouter } from 'expo-router'
import { theme } from '@theme'
import { UiPrimaryButton } from '@ui/index'
import { UiAlert } from '@ui/alert/UiAlert'
import { UiHeader } from '@ui/typography'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import { savingPlanService } from '@services'
import { Text } from 'react-native-paper'
import { useObserverState, useSetProgressPosition } from '@hooks'
import { DEFAULT_SAVING_PLAN__TOTAL_STEPS } from '@const/savingPlanSteps'
import SvgHouse from '../../assets/modern-house.svg'
import SvgHouse1 from '../../assets/scandinavian-house.svg'
import SvgCar from '../../assets/car-rental.svg'
import SvgFinance from '../../assets/finance.svg'
import SvgCheckIconFilled from '../../assets/check-circle-filled.svg'
import useSavingPlanStore, { clearSavingPlanStore } from '@store/saving-plan-store'

const numColumns = 2
const screenWidth = Dimensions.get('window').width
const itemMargin = 15
const itemSize = (screenWidth - itemMargin * (numColumns + 1)) / numColumns

const Icon = ({ icon }: { icon: string }) => {
  if (icon === 'car-rental') {
    return <SvgCar />
  }

  if (icon === 'modern-house') {
    return <SvgHouse1 />
  }

  if (icon === 'finance') {
    return <SvgFinance />
  }
  return <SvgHouse />
}

const GridItem = ({ item }) => {
  const { setTarget, selectedTarget } = useSavingPlanStore(state => ({
    setTarget: state.setTarget,
    selectedTarget: state.selectedTarget,
  }))
  return (
    <TouchableOpacity
      style={[
        styles.item,
        item.id === selectedTarget?.id && styles.itemSelected,
      ]}
      onPress={() => setTarget(item)}
    >
      {item.id === selectedTarget?.id && (
        <View style={styles.checkContainer}>
          <SvgCheckIconFilled width={24} height={24} />
        </View>
      )}
      <Icon icon={item.icon} />
      <Text
        style={[
          styles.text,
          item.id === selectedTarget?.id && { color: theme.colors.primary },
        ]}
      >
        {item.icon_label}
      </Text>
    </TouchableOpacity>
  )
}

export default () => {
  const router = useRouter()

  const { selectedTarget } = useSavingPlanStore(state => ({
    selectedTarget: state.selectedTarget,
  }))
  useSetProgressPosition(1, DEFAULT_SAVING_PLAN__TOTAL_STEPS)

  const onPress = useCallback(() => {
    router.push('/saving-plan-step-3')
  }, [])

  const data = useObserverState(null, savingPlanService.getSavingPlansTarget())

  return (
    <UiScreenContainer styles={styles.screenContainer}>
      <View style={styles.contentWrapper}>
        <FlatList
          data={data}
          renderItem={({ item }) => <GridItem item={item} />}
          keyExtractor={item => item.id}
          numColumns={numColumns}
          columnWrapperStyle={styles.row}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={[styles.scrollView, { paddingBottom: 100 }]}
          ListHeaderComponent={
            <>
              <UiHeader
                translatedText="savingPlan.savingGoalSelection.title"
                style={styles.header}
              />
              <UiAlert
                message="savingPlan.savingGoalSelection.info"
                type="info"
              />
              <View style={styles.gridContainer} />
            </>
          }
        />

        <UiPrimaryButton
          disabled={selectedTarget === undefined}
          onPress={onPress}
          translationTitle="common.continue"
          mode="contained"
          containerStyle={styles.fixedButton}
        />
      </View>
    </UiScreenContainer>
  )
}

const styles = StyleSheet.create({
  screenContainer: {
    backgroundColor: theme.colors.lilyWhite,
  },
  scrollView: {
    paddingBottom: 50,
  },
  header: {
    paddingRight: 20,
    textAlign: 'left',
    marginBottom: 20,
  },
  buttonContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    width: '100%',
    marginBottom: 30,
  },
  row: {
    justifyContent: 'space-between',
    marginBottom: itemMargin,
  },
  item: {
    width: itemSize,
    height: itemSize,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    backgroundColor: theme.colors.onPrimary,
    shadowColor: theme.colors.cyprus,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 10,
    elevation: 3,
  },
  text: {
    marginTop: 8,
    fontSize: 18,
    fontWeight: 400,
    color: theme.colors.black,
  },
  checkContainer: {
    position: 'absolute',
    right: 20,
    top: 20,
  },
  itemSelected: {
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  gridContainer: {
    marginTop: 20,
  },
  contentWrapper: {
    flex: 1,
  },
  fixedButton: {
    position: 'absolute',
    bottom: 20,
    left: 16,
    right: 16,
    zIndex: 10,
  },
})
