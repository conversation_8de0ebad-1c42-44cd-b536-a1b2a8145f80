import React, { useCallback, useState } from 'react'
import { StyleSheet, View } from 'react-native'
import { useRouter } from 'expo-router'
import { DEFAULT_SAVING_PLAN__TOTAL_STEPS } from '@const/savingPlanSteps'
import { useSetProgressPosition } from '@hooks'
import { theme } from '@theme'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import { UiPrimaryButton, UiStepper } from '@ui/index'
import { UiHeader } from '@ui/typography'
import { UiTitle } from '@ui/typography/UiTitle'
import { Chip } from 'react-native-paper'
import { useTranslations } from '@hooks'
import useSavingPlanStore from '@store/saving-plan-store'

const frequencies = ['week', 'month', 'quarter'] as const


export default () => {
  const router = useRouter()
  const t = useTranslations()
  const { savingAmountAndFrequency, setSavingAmountAndFrequency } =
    useSavingPlanStore(state => ({
      savingAmountAndFrequency: state.savingAmountAndFrequency,
      setSavingAmountAndFrequency: state.setSavingAmountAndFrequency,
    }))

  useSetProgressPosition(9, DEFAULT_SAVING_PLAN__TOTAL_STEPS)

  const onPress = useCallback(() => {
    router.push('/summary')
  }, [])

  return (
    <UiScreenContainer styles={styles.screenContainer}>
      <UiHeader
        translatedText="savingPlan.amountStep.title"
        style={styles.header}
      />
      <View style={styles.contentContainer}>
        <View>
          <UiTitle
            translatedText="savingPlan.amountStep.amountLabel"
            style={styles.amountLabel}
          />
          <UiStepper
            value={savingAmountAndFrequency.amount}
            onChange={amount =>
              setSavingAmountAndFrequency({
                amount,
                frequency: savingAmountAndFrequency.frequency,
              })
            }
            min={1}
            max={9999999}
            step={50}
            placeholder="0"
            unit="savingPlan.amountStep.unit"
          />
        </View>
        <View>
          <UiTitle
            translatedText="savingPlan.amountStep.frequencyLabel"
            style={styles.amountLabel}
          />
          <View style={styles.frequencyTags}>
            {frequencies.map(frequency => (
              <Chip
                key={frequency}
                mode="outlined"
                onPress={() =>
                  setSavingAmountAndFrequency({
                    amount: savingAmountAndFrequency.amount,
                    frequency ,
                  })
                }
                style={[
                  styles.chip,
                  savingAmountAndFrequency.frequency === frequency &&
                    styles.chipSelected,
                ]}
                textStyle={[
                  styles.chipText,
                  savingAmountAndFrequency.frequency === frequency &&
                    styles.chipTextSelected,
                ]}
              >
                {t(`savingPlan.amountStep.frequencies.${frequency}`)}
              </Chip>
            ))}
          </View>
        </View>
      </View>
      <UiPrimaryButton
        onPress={onPress}
        disabled={
          savingAmountAndFrequency.amount === 0 ||
          savingAmountAndFrequency.frequency === null
        }
        translationTitle="common.continue"
        mode="contained"
        containerStyle={styles.buttonContainer}
      />
    </UiScreenContainer>
  )
}

const styles = StyleSheet.create({
  screenContainer: {
    backgroundColor: theme.colors.lilyWhite,
  },
  header: {
    paddingRight: 20,
    textAlign: 'left',
    marginBottom: 0,
  },
  contentContainer: {
    display: 'flex',
    gap: 8,
  },
  amountLabel: {
    fontSize: 14,
    fontWeight: 400,
    color: theme.colors.mineralGreen,
    marginBottom: 4,
  },
  frequencyTags: {
    marginTop: 4,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  chip: {
    borderRadius: 100,
    paddingHorizontal: 14,
    paddingVertical: 7,
    marginBottom: 6,
    borderWidth: 1,
    borderColor: theme.colors.inputDefaultBorderColor,
    backgroundColor: theme.colors.lilyWhite,
  },
  chipText: {
    fontWeight: '400',
    fontSize: 16,
    color: theme.colors.text,
  },
  chipSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  chipTextSelected: {
    color: theme.colors.onPrimary,
  },
  buttonContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    width: '100%',
    marginBottom: 30,
  },
})
