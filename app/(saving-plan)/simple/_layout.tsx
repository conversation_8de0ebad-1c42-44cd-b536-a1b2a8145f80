import { useCallback } from 'react'
import { Stack } from 'expo-router'
import { authorizationService } from '@services'
import { theme } from '@theme'
import UiHeaderBackButton from '@ui/Buttons/UiHeaderBackButton'
import UiHeaderCloseButton from '@ui/Buttons/UiHeaderCloseButton'
import { HeaderProgress } from '@ui/header/headerProgress/headerProgress'
import useProgressStore from '@store/progress.store'

const SavingPlanLayout = () => {
  const progress = useProgressStore(state => state.progress)

  const overrideOnPress = useCallback(() => {
    authorizationService.unAuthorize()
  }, [])

  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
        title: '',
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: '',
          headerLeft: () => <UiHeaderBackButton />,
          headerTitle: () => (
            <HeaderProgress progress={progress} color={theme.colors.primary} />
          ),
          headerRight: () => <UiHeaderCloseButton />,
        }}
      />
    </Stack>
  )
}

export default SavingPlanLayout
