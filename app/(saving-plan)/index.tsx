import React, { useCallback } from 'react'
import { StyleSheet, View } from 'react-native'
import { useRouter } from 'expo-router'
import { theme } from '@theme'
import { UiPrimaryButton } from '@ui/index'
import { UiHeader } from '@ui/typography'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import { UiTitle } from '@ui/typography/UiTitle'
import UiFeatureSection from '@ui/section/UiFeatureSection'

export default () => {
  const router = useRouter()

  const onPress = useCallback(() => {
    router.push('/saving-plan-step-2')
  }, [])

  return (
    <UiScreenContainer styles={styles.screenContainer}>
      <UiHeader
        translatedText="savingPlan.formProps.title"
        style={styles.header}
      />
      <View style={styles.container}>
        <UiFeatureSection
          icon="bitcoin"
          headline="savingPlan.howItWorks.title"
          translatedText="savingPlan.howItWorks.description"
        />
        <UiFeatureSection
          icon="bitcoin"
          headline="savingPlan.whyItIsWorth.title"
          translatedText="savingPlan.whyItIsWorth.description"
        />
        <UiTitle
          translatedText="savingPlan.callToAction.text"
          style={styles.ctaText}
        />
      </View>
      <UiPrimaryButton
        onPress={onPress}
        translationTitle="common.start"
        mode="contained"
        containerStyle={styles.buttonContainer}
      />
    </UiScreenContainer>
  )
}

const styles = StyleSheet.create({
  screenContainer: {
    backgroundColor: theme.colors.lilyWhite,
  },
  header: {
    paddingRight: 20,
    textAlign: 'left',
  },
  container: {
    display: 'flex',
    gap: 16,
  },
  ctaText: {
    fontSize: 16,
    fontWeight: 500,
    color: theme.colors.mineralGreen,
  },
  buttonContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    width: '100%',
    marginBottom: 30,
  },
})
