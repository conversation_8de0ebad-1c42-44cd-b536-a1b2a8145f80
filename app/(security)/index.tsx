import React from 'react';
import { Image, StyleSheet, Text, View,  } from 'react-native';
import UiHeaderBackButton from '@ui/Buttons/UiHeaderBackButton';
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton';
import { useTranslations } from '@hooks';
import { UiHeaderButton } from '@ui/Buttons/UiHeaderButton';

export default function SecretPhraseScreen() {
  const t = useTranslations();

  const handleIntroductionPress = () => {
    console.log('Introduction Pressed');
  };

  const handleContinuePress = () => {
    console.log('Continue Pressed');
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <UiHeaderBackButton />
        <UiHeaderButton
          onPress={handleIntroductionPress}
          translationKey="common.introduction"
        />
      </View>
      <Image
        source={require('../../assets/security-image.png')}
        style={styles.image}
        resizeMode="contain"
      />
      <View style={styles.textContainer}>
        <Text style={styles.title}>
          {t('security.title')}
        </Text>
        <Text style={styles.description}>
          {t('security.description')}
        </Text>
      </View>
      <UiPrimaryButton
        translationTitle="common.continue"
        mode="contained"
        onPress={handleContinuePress}
        containerStyle={styles.buttonContainer}
      />
    </View>
  );
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0C0C0C',
    paddingHorizontal: 24,
    paddingTop: 48,
    paddingBottom: 32,
    justifyContent: 'space-between',
  },
  header: {
    position: 'absolute',
    top: 47,
    left: 16,
    right: 16,
    height: 40,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    zIndex: 10,
  },
  image: {
    width: 370,
    height: 370,
    alignSelf: 'center',
    marginTop: 87,
  },
  textContainer: {
    marginTop: 24,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '400',
    color: '#CCCCCC',
  },
  buttonContainer: {
    marginTop: 24,
  },
});