import { Stack } from 'expo-router'
import UiHeaderBackButton from '@ui/Buttons/UiHeaderBackButton'

const RegistrationLayout = () => {
  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
        title: '',
        headerLeft: () => <UiHeaderBackButton />,
      }}
    >
      <Stack.Screen
        name="sign-up"
        options={{
          title: '',
          headerLeft: () => <UiHeaderBackButton />,
        }}
      />
      <Stack.Screen
        name="index"
        options={{
          title: '',
          headerLeft: () => <UiHeaderBackButton />,
        }}
      />
      <Stack.Screen
        name="activation-code"
        options={{
          title: '',
          headerLeft: () => <UiHeaderBackButton />,
        }}
      />
    </Stack>
  )
}

export default RegistrationLayout
