import { UiHeader } from '@ui/typography'
import { EmailForm, SignWithGoogleButton } from '@components'
import { View } from 'react-native'
import UiDividerWithLabel from '@ui/divider/UiDividerWithLabel'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import React from 'react'

export default () => {
  return (
    <UiScreenContainer>
      <UiHeader
        translatedText="common.signUp"
        style={{
          textAlign: 'left',
        }}
      />
      <EmailForm redirectPath="/sign-up" />
      <View>
        <UiDividerWithLabel label="common.or" />
      </View>
      <SignWithGoogleButton/>
    </UiScreenContainer>
  )
}
