import { StyleSheet, View } from 'react-native'
import { UiHeader } from '@ui/typography'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import { TextInputField } from '@ui/TextInputField/TextInputField'
import { MaskedTextInput } from 'react-native-mask-text'
import React, { useCallback, useRef, useState } from 'react'
import style from '../../components/SignInForm/SignInForm.styles'
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'
import { errorHandlerService, ErrorKey, registrationService } from '@services'
import { ErrorContainer, ResendCode } from '@components'
import { useFocusEffect, useGlobalSearchParams, useRouter } from 'expo-router'

export default () => {
  const maskedInputRef = useRef()
  const [text, setText] = useState('')
  const [loading, setIsLoading] = useState(false)
  const router = useRouter()
  const params = useGlobalSearchParams()

  const onResend = useCallback(() => {
    setText('')
    errorHandlerService.clearError(ErrorKey.RegistrationError)
  }, [])

  useFocusEffect(() => {
    errorHandlerService.clearError(ErrorKey.RegistrationError)
  })

  const sendRegistrationCode = useCallback(() => {
    errorHandlerService.clearError(ErrorKey.RegistrationError)
    setIsLoading(true)
    registrationService
      .sendActivationCode(text, params?.username as string)
      .subscribe({
        next: value => {
          setIsLoading(false)
          router.push({
            pathname: '/activation-success',
            params: { username: params?.username as string },
          })
        },
        error: error => {
          errorHandlerService.add(
            ErrorKey.RegistrationError,
            text,
            error.status,
          )
          setIsLoading(false)
        },
      })
  }, [params, text])

  return (
    <UiScreenContainer>
      <UiHeader
        translatedText="activationCode.title"
        style={{
          textAlign: 'left',
        }}
      />
      <View>
        <ErrorContainer
          callback={() => {}}
          errorKey={ErrorKey.RegistrationError}
        />
      </View>
      <View style={styles.inputContainer}>
        <TextInputField
          value={text}
          onChangeText={(value) => setText(value)}
          keyboardType="numeric"
          render={props => (
            <MaskedTextInput
              {...props}
              mask="999-999"
              value={text}
              ref={maskedInputRef}
              onChangeText={(masked, unmasked) => setText(unmasked)}
              keyboardType="numeric"
              style={styles.maskTextInput}
            />
          )}
        />
        <View>
          <ResendCode
            username={params.username as string}
            onResend={onResend}
          />
        </View>
      </View>
      <View style={styles.buttonContainer}>
        <UiPrimaryButton
          loading={loading}
          disabled={text.length !== 6}
          translationTitle="signUp.buttons.moveForward.label"
          containerStyle={style.mainActionButtonContainer}
          mode="contained"
          onPress={sendRegistrationCode}
        />
      </View>
    </UiScreenContainer>
  )
}

const styles = StyleSheet.create({
  inputContainer: {
    flex: 3,
  },
  buttonContainer: {
    flex: 2,
    justifyContent: 'flex-end',
    paddingBottom: 30,
  },
  maskTextInput: {
    height: 58,
    minHeight: 58,
    marginVertical:10,
    textAlign: 'center',
    fontSize: 24,
    alignItems: 'center',
    paddingVertical:12,
    justifyContent:'center',
    letterSpacing: 8,
    borderRadius: 12,
  },
})
