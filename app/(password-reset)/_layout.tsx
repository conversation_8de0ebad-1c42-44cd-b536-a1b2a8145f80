import { Stack, useRouter } from 'expo-router'
import UiHeaderBackButton from '@ui/Buttons/UiHeaderBackButton'

const AuthorizationLayout = () => {
  const router = useRouter()

  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: '',
          headerLeft: () => <UiHeaderBackButton />,
        }}
      />
    </Stack>
  )
}

export default AuthorizationLayout
