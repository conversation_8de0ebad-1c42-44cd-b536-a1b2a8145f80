import {  Stack } from 'expo-router'
import UiHeaderBack<PERSON>utton from '@ui/Buttons/UiHeaderBackButton'

const AuthenticatedLayout = () => {
  return <Stack
    screenOptions={{
      headerShadowVisible: false,
      title: '',
    }}
  >
    <Stack.Screen
      name="index"
      options={{
        title: '',
        // headerLeft: () => (
        //   <UiHeaderBackButton overrideOnPress={overrideOnPress} />
        // ),
      }}
    />
  </Stack>
}

export default AuthenticatedLayout
