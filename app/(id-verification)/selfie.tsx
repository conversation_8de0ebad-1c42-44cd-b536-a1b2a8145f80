import { StyleSheet, View } from 'react-native'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import { CreatePhoto } from '@components'
import { Card } from 'react-native-paper'
import { useCallback, useState } from 'react'
import { theme } from '@theme'
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'
import { useRouter } from 'expo-router';

export default ()=> {
  const [image,setImage] = useState<string | undefined>(undefined)
  const onCreateImage = useCallback((image: string)=> {
    setImage(image)
  },[])

  const router = useRouter()

  const onPress= useCallback(()=> {

  }, [router])

  return <UiScreenContainer>
    <Card mode={'outlined'} contentStyle={styles.card} >
      <Card.Title title="Selfie" />
      <View style={styles.imageContainer}>
        {image &&
          (<Card.Cover source={{ uri: image }} style={styles.image} />)
        }
      </View>
      <Card.Actions style={styles.cardActions}>
        {!image
          ? <CreatePhoto title="verification.buttons.take-selfie" onCreateImage={onCreateImage}/>
          : <UiPrimaryButton translationTitle="common.continue" mode="outlined" onPress={onPress}/>
        }
      </Card.Actions>
    </Card>
  </UiScreenContainer>
}

const styles = StyleSheet.create({
  card:{backgroundColor:'white', borderColor: theme.colors.primary},
  imageContainer: {padding: 10, height:300},
  image: { borderColor: theme.colors.primary, borderStyle: 'solid', borderWidth: 1},
  cardActions: {alignItems:'center', justifyContent:'center'}
})
