import React, { useEffect, useState } from 'react'
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'
import { useRouter } from 'expo-router'
import { aisProcessService } from '@services'
import { firstValueFrom } from 'rxjs'
import { View, Text, StyleSheet, Modal, Image } from 'react-native'
import useAisProcessStore from '@store/ais.store'
import UiHeaderBackButton from '@ui/Buttons/UiHeaderBackButton'
import { IconButton } from 'react-native-paper'
import { theme } from '@theme'
import { useTranslations } from '@hooks'
import { BankActionButtons } from '../../components/BankConnection/BankActionButtons'
import { BankCard } from '../../components/BankConnection/BankCard'
import { UiHeaderButton } from '@ui/Buttons/UiHeaderButton'

export default () => {
  const router = useRouter()
  const t = useTranslations()
  const [isLoading, setIsLoading] = useState(false)
  const [showModal, setShowModal] = useState(false)
  const { bankAccounts, setBankAccounts, setCurrentProcess } = useAisProcessStore()

  useEffect(() => {
    const subscription = aisProcessService.getBankAccounts().subscribe({
      next: (fetchedAccounts) => {
        setBankAccounts(fetchedAccounts)
      },
      error: (error) => console.error('Error fetching bank accounts:', error)
    })
    
    return () => subscription.unsubscribe()
  }, [setBankAccounts])

  const handleCreateProcess = async () => {
    setIsLoading(true)
    const process = await firstValueFrom(aisProcessService.createProcess())
    if (process?.url) {
      setCurrentProcess(process)
      router.push({
        pathname: '/(bank-connection)/webview',
        params: { url: process.url }
      })
    }
    setIsLoading(false)
  }

  const toggleModal = () => setShowModal(!showModal)

  const handleAccountDetails = (accountId: string) => {
    router.push({
      pathname: '/(bank-connection)/details',
      params: { accountId } 
    })
  }

  const handleRenewConnection = (accountId: string) => {
    console.log('Renew connection for account:', accountId)
  }

  const showExpiryTooltip = () => {
    alert(t('bank-connection.expiryTooltip') || 'When there are less than 30 days until connection expiration')
  }

  const shouldShowRenewButton = (createDate: Date): boolean => {
    const expiryDate = new Date(createDate)
    expiryDate.setMonth(expiryDate.getMonth() + 3)
    const now = new Date()
    const diffInDays = (expiryDate.getTime() - now.getTime()) / (1000 * 3600 * 24)
    return diffInDays < 30
  }

  const handleDisconnectAccount = (accountId: string) => {
    console.log('Disconnect account:', accountId)
  }

  const formatAccountNumber = (accountNumber: string) => {
    if (!accountNumber) return '•••• •••• •••• ••••'
    const visibleDigits = 4
    const maskedPart = '•'.repeat(Math.max(0, accountNumber.length - visibleDigits))
    return `${maskedPart}${accountNumber.slice(-visibleDigits)}`
  }

  return (
    <View style={styles.safeArea}>
      <View style={[styles.container, bankAccounts.length > 0 && styles.hasAccounts]}>
        <View style={styles.header}>
          <UiHeaderBackButton />
          <IconButton 
            icon="help" 
            size={18} 
            onPress={toggleModal}
            containerColor={theme.headerLeft.background}
            style={styles.infoButton}
          />
        </View>

        <View style={styles.content}>
          <View>
            <View style={styles.bankHeader}>
              <Text style={styles.bankTitle}>{t('bank-connection.title')}</Text>
              <Text style={styles.bankSubtitle}>{t('bank-connection.description')}</Text>
            </View>
            
            {bankAccounts ? (
              <View style={styles.bankList}>
                {bankAccounts.map(account => (
                  <BankCard
                    key={account.accountId}
                    logo={require('../../assets/logo.png')}
                    name={account.schemeName || account.nameClient || 'Bank Account'}
                    accountNumber={formatAccountNumber(account.accountNumber)}
                    allowUnmask={false}
                  >
                    <View style={styles.bankExpiryRow}>
                      <View style={styles.expiryLabelContainer}>
                        <Text style={styles.bankExpiryText}>{t('common.expires')}</Text>
                        <IconButton 
                          icon="information-outline" 
                          size={16} 
                          onPress={showExpiryTooltip}
                          style={styles.tooltipIcon}
                        />
                      </View>
                      <Text style={styles.bankExpiryText}>
                        {new Date(account.createDate).toLocaleDateString()}
                      </Text>
                    </View>

                    {shouldShowRenewButton(new Date(account.createDate)) && (
                      <UiHeaderButton
                        translationTitle="bank-connection.renewConnection"
                        onPress={() => handleRenewConnection(account.accountId)}
                      />
                    )}

                    <BankActionButtons
                      onLeftButtonClick={() => handleDisconnectAccount(account.accountId)}
                      onRightButtonClick={() => handleAccountDetails(account.accountId)}
                      rightButtonText="bank-connection.details"
                    />
                  </BankCard>
                ))}
              </View>
            ) : (
              <View style={styles.emptyContainer}>
                <Image
                  source={require('../../assets/no-banks-image.png')}
                  style={styles.noBanksImage}
                  resizeMode="contain"
                />
                <Text style={styles.emptyText}>
                  {t('bank-connection.noAccounts')}
                </Text>
              </View>
            )}
          </View>
        </View>

        <UiPrimaryButton
          translationTitle="bank-connection.buttonTitle"
          onPress={handleCreateProcess}
          mode="contained"
          containerStyle={styles.buttonContainer}
          loading={isLoading}
          disabled={isLoading}
        />

        <Modal
          visible={showModal}
          transparent={true}
          animationType="slide"
          onRequestClose={toggleModal}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <Text>{t('bank-connection.helpModalContent')}</Text>
              <IconButton 
                icon="close" 
                onPress={toggleModal} 
                style={styles.closeButton}
              />
            </View>
          </View>
        </Modal>
      </View>
    </View>
  )
}
const styles = StyleSheet.create({
  hasAccounts: {
    backgroundColor: 'white',
  },
  safeArea: {
    flex: 1,
    backgroundColor: 'white',
  },
  container: {
    flex: 1,
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 50,
  },
  hasProcess: {
    backgroundColor: 'white',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  infoButton: {
    marginRight: -4,
  },
  content: {
    marginTop: 12,
  },
  processInfo: {
    padding: 12,
    marginBottom: 16,
    borderRadius: 8,
  },
  processText: {
    color: '#FFFFFF',
    fontSize: 14,
  },
  buttonContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    width: '100%',
  },
  sectionHeader: {
    marginBottom: 16,
  },
  bankHeader: {
    marginBottom: 24,
    gap: 8,
  },
  bankTitle: {
    fontSize: 32,
    color: '#2A322F',
  },
  bankSubtitle: {
    fontSize: 16,
    color: '#3F3F3F',
    lineHeight: 24,
  },
  bankList: {
    gap: 16,
    marginBottom: 24,
  },
  bankCard: {
    width: '100%',
    padding: 16,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: '#DEE3E1',
    backgroundColor: '#F9FAFA',
  },
  bankHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  bankLogo: {
    width: 32,
    height: 32,
    marginRight: 12,
  },
  bankName: {
    fontSize: 16,
    color: '#3F3F3F',
    lineHeight: 24,
    fontWeight: '400',
  },
  bankAccount: {
    fontSize: 16,
    color: '#3F3F3F',
    lineHeight: 24,
    marginBottom: 16,
  },
  bankExpiryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  bankExpiryText: {
    fontWeight: '400',
    fontSize: 16,
    lineHeight: 24,
    color: '#3F3F3F',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    alignItems: 'center',
  },
  closeButton: {
    marginTop: 10,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 48,
    marginBottom: 32,
    gap: 24,
},
  noBanksImage: {
    width: "100%", 
    height: 170,
    opacity: 0.9,
},
  emptyText: {
    textAlign: 'center',
    color: '#8A9E96',
    fontSize: 16,
    lineHeight: 24,
},
  expiryLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
},
  tooltipIcon: {
    marginLeft: 4,
    marginTop: -2,
    padding: 0,
},
  renewalButtonContainer: {
    marginBottom: 16,
},
  renewalButton: {
    height: 32,
    backgroundColor: '#00C897', 
},
  blackButton: {
    backgroundColor: '#2A322F',
},
  blackButtonText: {
    color: '#FFFFFF',
},
});
