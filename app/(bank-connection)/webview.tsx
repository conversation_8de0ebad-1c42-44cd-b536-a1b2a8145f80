import React from 'react'
import { WebView } from 'react-native-webview'
import { StyleSheet, View, Text } from 'react-native'
import { useLocalSearchParams } from 'expo-router'
import { theme } from '@theme'
import { useTranslations } from '@hooks'

export default function AisWebViewScreen() {
  const { url } = useLocalSearchParams()
  const t = useTranslations()

  if (!url) {
    return (
      <View style={styles.container}>
        <Text>{t("messages.urlError")}</Text>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <WebView
        source={{ uri: url as string }}
        style={styles.webview}
        startInLoadingState={true}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  webview: {
    flex: 1,
  },
})