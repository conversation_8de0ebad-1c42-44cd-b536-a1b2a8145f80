import React from 'react'
import { View, Text, StyleSheet, SafeAreaView } from 'react-native'
import UiHeaderBackButton from '@ui/Buttons/UiHeaderBackButton'
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'
import { BankCard } from '../../components/BankConnection/BankCard'
import { BankActionButtons } from '../../components/BankConnection/BankActionButtons'
import { useRouter, useLocalSearchParams } from 'expo-router'
import { useTranslations } from '@hooks'
import useAisProcessStore from '@store/ais.store'

const Details = () => {
  const router = useRouter()
  const t = useTranslations()
  const { accountId } = useLocalSearchParams()
  const { bankAccounts } = useAisProcessStore()
  const bank = bankAccounts.find(account => account.accountId === accountId)
  if (!bank) {
    return (
      <SafeAreaView style={styles.safe}>
        <View style={styles.container}>
          <Text style={styles.screenTitle}>{t('common.notFound')}</Text>
        </View>
      </SafeAreaView>
    )
  }
  return (
    <View style={styles.safe}>
      <View style={styles.container}>
        <View style={styles.headerRow}>
          <UiHeaderBackButton />
          <Text style={styles.screenTitle}>{t('bank-connection.details')}</Text>
        </View>

        <BankCard
          logo={bank.logo || require('../../assets/logo.png')}
          name={bank.name}
          accountNumber={bank.accountNumber}
          allowUnmask={true}
        />

        <View style={styles.fieldGroup}>
        <View style={styles.row}>
            <Text style={styles.label}>{t('bank-connection.connectionDate')}:</Text>
            <Text style={styles.value}>{bank.connectionDate}</Text>
        </View>

        <View style={styles.row}>
            <Text style={styles.label}>{t('bank-connection.lastRenew')}:</Text>
            <Text style={styles.value}>{bank.renewalDate}</Text>
        </View>

        <View style={styles.row}>
            <Text style={styles.label}>{t('bank-connection.expiryDate')}:</Text>
            <Text style={styles.value}>{bank.expiryDate}</Text>
        </View>
        </View>
        <BankActionButtons 
          onLeftButtonClick={() => console.log('disconnect', accountId)} 
          onRightButtonClick={() => console.log('renew', accountId)} 
        />


        <UiPrimaryButton 
            translationTitle="common.back"
            onPress={() => router.back()}
            mode="contained"
            containerStyle={styles.buttonContainer}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  safe: {
    flex: 1,
    backgroundColor: 'white'
  },
  container: {
    flex: 1,
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 50,
  },
  headerRow: {
    marginBottom: 24,
    flexDirection: 'column',
    gap: 16
  },
  screenTitle: {
    fontSize: 32,
    color: '#2A322F',
  },
  fieldGroup: {
    marginTop: 24,
    gap: 16
  },
  label: {
    fontSize: 16,
    color: '#6F6F6F'
  },
  value: {
    fontSize: 16,
    color: '#2A322F'
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  buttonContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    width: '100%',
  },
})

export default Details