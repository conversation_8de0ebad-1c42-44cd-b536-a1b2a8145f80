import { Dimensions, StyleSheet, View } from 'react-native'
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'
import { useCallback, useEffect } from 'react'
import { useRouter } from 'expo-router'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import { theme } from '@theme'
import Animated, {
  Easing,
  FadeIn,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withTiming,
} from 'react-native-reanimated'
import { Separator } from '@ui/separator/Separator'
import { Logo } from '@ui/logo/Logo'
import { UiTitle } from '@ui/typography/UiTitle'
import { Image } from 'expo-image'
const { width } = Dimensions.get('window')

export default () => {
  const router = useRouter()

  const onSignUpPress = useCallback(() => {
    router.push('/(registration)')
  }, [])

  const onSignInPress = useCallback(() => {
    router.push('/(authorization)')
  }, [])

  const animatedHeight = useSharedValue(500)

  useEffect(() => {
    animatedHeight.value = withDelay(
      200,
      withTiming(0, {
        duration: 250,
        easing: Easing.bezier(0.25, 0.46, 0.45, 0.94),
      }),
    )
  }, [])

  const animatedStyles = useAnimatedStyle(() => {
    return {
      top: animatedHeight.value,
    }
  })

  return (
    <UiScreenContainer styles={styles.screenContainer}>
      <Animated.View
        entering={FadeIn.delay(800).duration(600)}
        style={styles.splashImageContainer}
      >
        <Image
          source={require('../assets/splash-image.png')}
          contentFit="cover"
          style={styles.splashImage}
        />
      </Animated.View>
      <View style={{ flex: 1 }} />
      <View style={{ flex: 1 }}>
        <Animated.View style={[styles.card, animatedStyles]}>
          <View>
            <Logo />
            <UiTitle
              translatedText="screens.home.title"
              style={{
                textAlign: 'center',
              }}
            />
          </View>
          <View style={styles.buttonContainer}>
            <UiPrimaryButton
              onPress={onSignInPress}
              translationTitle="common.signIn"
              mode="outlined"
              buttonStyle={{ borderColor: theme.colors.primary }}
            />
            <Separator />
            <UiPrimaryButton
              onPress={onSignUpPress}
              translationTitle="common.signUp"
              mode="contained"
            />
          </View>
        </Animated.View>
      </View>
    </UiScreenContainer>
  )
}

const styles = StyleSheet.create({
  card: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-around',
    padding: 20,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    width: '100%',
    backgroundColor: theme.colors.background,
    marginHorizontal: 0,
  },
  screenContainer: {
    backgroundColor: theme.colors.primary,
    marginHorizontal: 0,
    paddingHorizontal: 0,
  },
  splashImageContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '85%',
  },
  splashImage: {
    top: 0,
    width,
    aspectRatio: 3 / 4,
    position: 'absolute',
    height: undefined,
  },
  buttonContainer: {
    width: '100%',
  },
})
