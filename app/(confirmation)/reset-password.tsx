import { ConfirmationScreen } from '@components'
import { useCallback, useEffect } from 'react'
import { useFocusEffect, useGlobalSearchParams, useRouter } from 'expo-router'
import { authorizationService } from '@services'

export default () => {
  const router = useRouter()
  const params = useGlobalSearchParams()

  const onButtonPress = useCallback(() => {
    router.replace({ pathname: '/sign-in', params: params.email })
  }, [])

  useFocusEffect(() => {
    if (params?.email) {
      authorizationService.resetPassword(params.email as string)
    }
  })

  return (
    <ConfirmationScreen
      buttonTitle="common.signIn"
      message="messages.activationLinkSent"
      onButtonPress={onButtonPress}
    />
  )
}
