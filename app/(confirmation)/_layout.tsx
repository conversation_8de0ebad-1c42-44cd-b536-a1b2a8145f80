import { Stack } from 'expo-router'
import UiHeaderBackButton from '@ui/Buttons/UiHeaderBackButton'
import { HeaderLogo } from '@ui/header/headerLogo/headerLogo'

export default () => {
  return (
    <Stack
      screenOptions={{
        headerShadowVisible: false,
        headerLeft: () => <UiHeaderBackButton />,
        title: '',
        headerTitle: () => <HeaderLogo />,
      }}
    >
      <Stack.Screen name="index" />
      <Stack.Screen name="activation-success" />
      <Stack.Screen name="reset-password" />
      <Stack.Screen name="saving-plan-created" />
    </Stack>
  )
}
