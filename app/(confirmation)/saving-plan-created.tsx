import { ConfirmationScreen } from '@components'
import { useCallback } from 'react'
import { useFocusEffect, useGlobalSearchParams, useRouter } from 'expo-router'

const REDIRECT_TIMEOUT = 4000

export default () => {
  const router = useRouter()
  const params = useGlobalSearchParams()

  const onButtonPress = useCallback(() => {
    router.replace({pathname: '/(authenticated)'})
  }, [router,params])

  useFocusEffect(()=>{
    setTimeout(()=> {
      onButtonPress()
    }, REDIRECT_TIMEOUT)
  })

  return (
    <ConfirmationScreen
      buttonTitle="common.continue"
      message="messages.planCreated"
      onButtonPress={onButtonPress}
    />
  )
}
