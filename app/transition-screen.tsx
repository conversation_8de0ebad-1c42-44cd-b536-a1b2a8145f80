import Animated from 'react-native-reanimated'
import { UiScreenContainer } from '@ui/containers/UiScreenContainer'
import { Keyboard } from 'react-native'
import { UiTitle } from '@ui/typography/UiTitle'
import { useGlobalSearchParams } from 'expo-router'
import { theme } from '@theme'
import { useEffect } from 'react'

export default () => {
  const params = useGlobalSearchParams()

  useEffect(() => {
    Keyboard.dismiss()
  }, [])

  return (
    <UiScreenContainer>
      <Animated.View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Animated.View>
          {params?.message && (
            <UiTitle
              style={{ color: theme.colors.primary }}
              translatedText={params.message as string}
            />
          )}
        </Animated.View>
      </Animated.View>
    </UiScreenContainer>
  )
}
