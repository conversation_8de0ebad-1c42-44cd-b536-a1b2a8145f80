import React, { useCallback, useRef } from 'react'
import { View, StyleSheet, ScrollView, Dimensions, Text, TouchableOpacity, Image } from 'react-native'
import { UiPrimaryButton } from '@ui/Buttons/UiPrimaryButton'
import { UiText } from '@ui/typography/UiText'
import { authorizationService } from '@services'
import { theme } from '@theme'
import { useObserverState } from '@hooks'
import { userService } from '@services'
import { clearSavingPlanStore } from '@store/saving-plan-store'
import { clearAisProcessStore } from '@store/ais.store'
import { useRouter } from 'expo-router'
import { LineChart } from 'react-native-gifted-charts'
import { LinearGradient } from 'expo-linear-gradient'
import useTranslations from 'hooks/useTranslations'
import Carousel, { ICarouselInstance } from 'react-native-reanimated-carousel'
import Animated, {
  useAnimatedStyle,
  interpolate,
  Extrapolate,
  useSharedValue,
  interpolateColor,
} from 'react-native-reanimated';
import Svg, { Path } from 'react-native-svg';

const screenWidth = Dimensions.get("window").width

interface Transaction {
  id: number;
  type: string;
  date: string;
  amount: number;
  btc: number;
}

const CarouselIndicator: React.FC<{
  index: number
  length: number
  animValue: Animated.SharedValue<number>
}> = (props) => {
  const { animValue, index, length } = props;

  const animStyle = useAnimatedStyle(() => {
    const value = animValue.value;

    const inputRange = [index - 1, index, index + 1];
    const outputSizeRange = [4, 6, 4];
    const outputColorRange = ['#DEE3E1', '#1FAD8C', '#DEE3E1'];
    
    if (index === 0) {
      inputRange.push(length - 1, length);
      outputSizeRange.push(4, 6);
      outputColorRange.push('#DEE3E1', '#1FAD8C');
    }

    if (index === length - 1) {
      inputRange.unshift(-1);
      outputSizeRange.unshift(6);
      outputColorRange.unshift('#1FAD8C');
    }
    
    const size = interpolate(
      value,
      inputRange,
      outputSizeRange,
      Extrapolate.CLAMP,
    );

    const backgroundColor = interpolateColor(
      value,
      inputRange,
      outputColorRange,
    );

    return {
      width: size,
      height: size,
      borderRadius: size / 2,
      backgroundColor,
    };
  }, [animValue, index, length]);

  return <Animated.View style={[styles.indicator, animStyle]} />;
};

const ArrowUpRightIcon = () => (
  <Svg width="12" height="13" viewBox="0 0 12 13" fill="none">
    <Path 
      d="M3.5 9L8.5 4M8.5 4L3.5 4M8.5 4L8.5 9" 
      stroke="#24D327" 
      strokeLinecap="round" 
      strokeLinejoin="round"
    />
  </Svg>
);


interface QuickAction {
  id: string;
  type: 'configuration' | 'verification' | 'bankConnection';
  titleKey: string;
  buttonKey: string;
  highlightWordKey?: string;
  image: any;
  route: string;
}

const mockData = {
  totalBalance: 345657.12,
  portfolio: 0.9832,
  savingsValue: 345657.12,
  depositedAmount: 250000.00,
  bitcoinValue: 366181.12,
  bitcoinChangePercent: 10.2,
  bitcoinChangeTimeframe: '1t',
  quickActions: [
    {
      id: 'configuration',
      type: 'configuration' as const,
      titleKey: 'dashboard.configurationBanner.text',
      buttonKey: 'dashboard.configurationBanner.button',
      highlightWordKey: 'dashboard.configurationBanner.highlightWord',
      image: require('../../assets/configuration-illustration.png'),
      route: '(saving-plan)' as const
    },
    {
      id: 'verification',
      type: 'verification' as const,
      titleKey: 'dashboard.verificationBanner.text',
      buttonKey: 'dashboard.verificationBanner.button',
      highlightWordKey: 'dashboard.verificationBanner.highlightWord',
      image: require('../../assets/security-image.png'),
      route: '(id-verification)' as const
    }
  ] as QuickAction[],
  savingsChartData: [
    { value: 0, hideDataPoint: true },
    { value: 2000, hideDataPoint: true },
    { value: 3000, hideDataPoint: true },
    { value: 6000, hideDataPoint: true },
    { value: 5000, hideDataPoint: true },
    { value: 6000, hideDataPoint: true },
    { value: 11000, hideDataPoint: true },
    { value: 15500, hideDataPoint: true },
    { value: 17000 },
  ],
  depositedChartData: [
    { value: 0, hideDataPoint: true },
    { value: 1000, hideDataPoint: true },
    { value: 2000, hideDataPoint: true },
    { value: 3000, hideDataPoint: true },
    { value: 4000, hideDataPoint: true },
    { value: 5000, hideDataPoint: true },
    { value: 6000, hideDataPoint: true },
    { value: 8000, hideDataPoint: true },
    { value: 9000 },

  ],
  transactions: [
    { id: 1, type: 'deposit', date: '2024-06-20T13:40:00Z', amount: 50, btc: 0.00013 },
    { id: 2, type: 'deposit', date: '2024-06-19T15:40:00Z', amount: 50, btc: 0.00013 },
    { id: 3, type: 'deposit', date: '2024-06-18T09:40:00Z', amount: 50, btc: 0.00013 },
  ] as Transaction[]
}

const FadingVerticalLines = () => {
  const numberOfLines = mockData.savingsChartData.length
  const lineStyle = {
    width: 1,
    height: 160,
  };

  return (
    <View style={styles.verticalLinesContainer}>
      {Array.from({ length: numberOfLines }).map((_, index) => (
        <LinearGradient
          key={index}
          colors={['rgba(222, 227, 225, 0)', 'rgba(222, 227, 225, 0.5)']}
          style={lineStyle}
        />
      ))}
    </View>
  );
};

const DashedBottomLine = () => {
  return (
    <View style={styles.dashedLineContainer}>
      {Array.from({ length: 40 }).map((_, index) => (
        <View key={index} style={styles.dash} />
      ))}
    </View>
  );
};

export default function Index() {
  const onPress = useCallback(async () => {
    clearSavingPlanStore();
    clearAisProcessStore();
    await authorizationService.unAuthorize()
  }, [])

  const router = useRouter()
  const userInfo = useObserverState(null, userService.getUserInfo())
  const t = useTranslations()

  const carouselRef = useRef<ICarouselInstance>(null)
  const quickActions = mockData.quickActions
  const progressValue = useSharedValue<number>(0);


  const HighlightedText = ({ text, highlightWord }: { text: string, highlightWord?: string }) => {
    
    if (!highlightWord) {
      return <Text style={styles.configText}>{text}</Text>
    }

    const lowerText = text.toLowerCase()
    const lowerHighlight = highlightWord.toLowerCase()
    const startIndex = lowerText.indexOf(lowerHighlight)
    
    if (startIndex === -1) {
      return <Text style={styles.configText}>{text}</Text>
    }

    const beforeText = text.substring(0, startIndex)
    const highlightText = text.substring(startIndex, startIndex + highlightWord.length)
    const afterText = text.substring(startIndex + highlightWord.length)
    
    return (
      <Text style={styles.configText}>
        {beforeText && <Text style={styles.configText}>{beforeText}</Text>}
        <Text style={[styles.configText, styles.highlightedText]}>{highlightText}</Text>
        {afterText && <Text style={styles.configText}>{afterText}</Text>}
      </Text>
    )
  }

  const renderQuickActionSlide = ({item}: {item: QuickAction}) => {
    const text = t(item.titleKey)
    const highlightWord = item.highlightWordKey ? t(item.highlightWordKey) : undefined

    return (
      <View style={{ paddingHorizontal: 16 }}>
        <View style={styles.configBanner}>
          <View style={styles.configContent}>
            <HighlightedText text={text} highlightWord={highlightWord} />
            <UiPrimaryButton
              translationTitle={item.buttonKey}
              mode="contained"
              onPress={() => router.push(item.route as any)}
              containerStyle={{ width: 'auto' }}
              buttonStyle={styles.configButton}
              contentStyle={styles.configButtonContent}
              labelStyle={styles.configButtonText}
            />
          </View>
          <View style={styles.configImageContainer}>
            <Image
              source={item.image}
              style={styles.configIllustration}
              resizeMode="contain"
            />
          </View>
        </View>
      </View>
    )
  }

  const QuickActionsSlider = () => {
    if (quickActions.length <= 1) {
      return quickActions.length > 0 ? renderQuickActionSlide({item: quickActions[0]}) : null;
    }
    
    return (
      <View style={styles.sliderContainer}>
        <Carousel
          ref={carouselRef}
          loop
          width={screenWidth}
          height={132}
          autoPlay={true}
          autoPlayInterval={4000}
          data={quickActions}
          scrollAnimationDuration={450}
          onProgressChange={(_, absoluteProgress) => (progressValue.value = absoluteProgress)}
          renderItem={renderQuickActionSlide}
        />

        {/* Slider indicators */}
        <View style={styles.sliderIndicators}>
          {quickActions.map((_, index) => (
            <CarouselIndicator
              animValue={progressValue}
              index={index}
              key={index}
              length={quickActions.length}
            />
          ))}
        </View>
      </View>
    )
  }

  const SavingsChart = () => {
    const numberOfPoints = mockData.savingsChartData.length;
    const containerWidth = screenWidth - 64;
    const calculatedSpacing = numberOfPoints > 1 ? containerWidth / (numberOfPoints - 1) : 0;
    
    const allValues = [...mockData.savingsChartData.map(d => d.value), ...mockData.depositedChartData.map(d => d.value)];
    const maxValue = Math.max(...allValues);
    const minValue = Math.min(...allValues);
    const stepValue = (maxValue - minValue) / 5;
    
    return (
      <View style={styles.chartCard}>
                  <View style={styles.chartHeader}>
            <Text style={styles.chartAmount}>{mockData.savingsValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).replace(',', ' ')} PLN</Text>
            <Text style={styles.chartSubtext}>{t('dashboard.deposited')} {mockData.depositedAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).replace(',', ' ')} PLN</Text>
          </View>
       
        <View style={styles.chartContainer}>
          <LineChart
            areaChart
            data={mockData.savingsChartData}
            data2={mockData.depositedChartData}
            height={160}
            width={containerWidth}
            spacing={calculatedSpacing}
            maxValue={maxValue}
            mostNegativeValue={minValue}
            stepValue={stepValue}
            noOfSections={5}
            disableScroll
            color1="#F38600"
            color2="#40C1A3"
            dataPointsColor1="#F38600"
            dataPointsColor2="#40C1A3"
            dataPointsRadius={4}
            curved
            thickness1={3}
            thickness2={3}
            animateOnDataChange
            animationDuration={1000}
            startFillColor1="rgba(243, 134, 0, 0.1)"
            startFillColor2="rgba(64, 193, 163, 0.1)"
            endFillColor1="rgba(243, 134, 0, 0.02)"
            endFillColor2="rgba(64, 193, 163, 0.02)"
            startOpacity={0.1}
            endOpacity={0.02}
            initialSpacing={0}
            yAxisTextStyle={{ color: 'transparent' }}
            xAxisThickness={0}
            yAxisThickness={0}
            hideRules
            showVerticalLines={false}
            hideYAxisText
            yAxisLabelWidth={0}
            overflowBottom={-7}
            xAxisLabelsHeight={0}
          />
          <FadingVerticalLines />
          <DashedBottomLine />
        </View>
         
        <View style={styles.chartLegend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#F38600' }]} />
            <Text style={[styles.legendText, { color: '#F38600' }]}>{t('dashboard.savingsChart.legend.savingsValue')}</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: '#40C1A3' }]} />
            <Text style={[styles.legendText, { color: '#40C1A3' }]}>{t('dashboard.savingsChart.legend.depositedAmount')}</Text>
          </View>
        </View>
      </View>
    )
  }

  const BitcoinCard = () => (
    <View style={styles.bitcoinCard}>
      <View style={styles.bitcoinLeft}>
        <Image 
          source={require('../../assets/bitcoin.png')} 
          style={styles.bitcoinIcon}
          resizeMode="contain"
        />
        <View>
          <Text style={styles.bitcoinTitle}>Bitcoin</Text>
          <Text style={styles.bitcoinSymbol}>BTC</Text>
        </View>
      </View>
             <View style={styles.bitcoinRight}>
         <Text style={styles.bitcoinValue}>{mockData.bitcoinValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).replace(',', ' ')} PLN</Text>
         <View style={styles.bitcoinChange}>
           <Text style={styles.bitcoinChangeText}>+ {mockData.bitcoinChangePercent}% ({mockData.bitcoinChangeTimeframe})</Text>
           <View style={styles.arrowIconContainer}>
             <ArrowUpRightIcon />
           </View>
         </View>
       </View>
    </View>
  )

  const TransactionItem = ({ transaction }: { transaction: Transaction }) => (
    <View style={styles.transactionItem}>
      <View style={styles.transactionLeft}>
        <Image 
          source={require('../../assets/transaction-icon.png')} 
          style={styles.transactionIcon}
          resizeMode="contain"
        />
                  <View style={styles.transactionInfo}>
            <Text style={styles.transactionType}>{t(`dashboard.transactions.types.${transaction.type}`)}</Text>
            <Text style={styles.transactionDate}>{new Date(transaction.date).toLocaleDateString()}</Text>
          </View>
      </View>
      <View style={styles.transactionRight}>
        <Text style={styles.transactionAmount}>+{transaction.amount.toLocaleString('en-US')} PLN</Text>
        <Text style={styles.transactionBtc}>{transaction.btc.toLocaleString('en-US', { minimumFractionDigits: 5, maximumFractionDigits: 5 })} BTC</Text>
      </View>
    </View>
  )

  const TransactionsSection = () => (
    <View style={styles.transactionsCard}>
      <View style={styles.transactionsList}>
        {mockData.transactions.map((transaction, index) => (
          <View key={transaction.id}>
            <TransactionItem transaction={transaction} />
            {index < mockData.transactions.length - 1 && <View style={styles.transactionDivider} />}
          </View>
        ))}
      </View>
      <UiPrimaryButton
        translationTitle="dashboard.buttons.seeAll"
        mode="contained"
        onPress={() => router.push('(authenticated)/transactions')}
        containerStyle={{ width: '100%' }}
        buttonStyle={styles.seeAllButton}
        contentStyle={styles.seeAllButtonContent}
        labelStyle={styles.seeAllText}
      />
    </View>
  )

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
                      <TouchableOpacity style={styles.userInfo} onPress={() => router.push('(navigation)')}>
              <View style={styles.avatarPlaceholder} />
              <Text style={styles.greeting}>{t('dashboard.greeting')} {userInfo?.username || 'Marek'}!</Text>
            </TouchableOpacity>
        </View>

        {/* Balance Section */}
        <View style={styles.balanceSection}>
          <UiText translatedText="dashboard.totalBalance" variant="titleMedium" style={styles.balanceLabel} />
          <Text style={styles.balanceAmount}>
            {Number((mockData.portfolio * mockData.bitcoinValue).toFixed(2)).toLocaleString('en-US').replace(',', ' ')} PLN
          </Text>
          <Text style={styles.balanceBtc}>≈ {mockData.portfolio.toLocaleString('en-US', { minimumFractionDigits: 4, maximumFractionDigits: 4 })} BTC</Text>
        </View>

        {/* Quick Actions Slider */}
        <QuickActionsSlider />

        {/* Savings Section */}
        <View style={styles.section}>
          <UiText translatedText="dashboard.sections.savings" variant="titleMedium" style={styles.sectionTitle} />
          <SavingsChart />
          <BitcoinCard />
        </View>

        {/* Transactions Section */}
        <View style={styles.section}>
          <UiText translatedText="dashboard.sections.transactions" variant="titleMedium" style={styles.sectionTitle} />
          <TransactionsSection />
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.lilyWhite,
  },
  scrollView: {
    flex: 1,
    paddingBottom: 80,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 52
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  avatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#DEE3E1',
  },
  greeting: {
    fontSize: 18,
    fontWeight: '400',
    color: '#3F3F3F',
    lineHeight: 22,
  },
  logoutButton: {
    height: 36,
    width: 92,
    borderColor: theme.colors.inputDefaultBorderColor,
    backgroundColor: theme.colors.lilyWhite,
  },
  logoutContent: {
    height: 36,
  },
  logoutLabel: {
    marginHorizontal: 0,
    marginVertical: 0,
    fontSize: 16,
    color: theme.colors.text,
  },
  balanceSection: {
    alignItems: 'center',
    marginTop: 48,
    marginBottom: 64,
    paddingHorizontal: 16,
  },
  balanceLabel: {
    fontSize: 16,
    color: '#6F867D',
    marginBottom: 16,
    fontWeight: '400',
  },
  balanceAmount: {
    fontSize: 40,
    fontWeight: '500',
    color: '#000000', 
    letterSpacing: -1,
    textAlign: 'center',
    lineHeight: 48,
  },
  balanceBtc: {
    fontSize: 16,
    color: '#586A63',
    marginTop: 4,
    lineHeight: 19.2,
  },
  configBanner: {
    backgroundColor: '#131615',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#0D3D32',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 4,
    position: 'relative',
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'center',
  },
  configContent: {
    flex: 1,
    zIndex: 1,
  },
  configText: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 16,
    lineHeight: 19.2,
  },
  highlightedText: {
    color: '#7DE8CF',
  },
  configImageContainer: {
    position: 'absolute',
    bottom: -16,
    right: 0,
    height: '100%',
    width: 120,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  configIllustration: {
    width: 150,
    height: 150,
  },
  sliderContainer: {
    marginBottom: 24,
  },
  sliderIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
    height: 6,
    marginTop: 8,
  },
  indicator: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#DEE3E1',
  },
  configButton: {
    backgroundColor: '#1FAD8C',
    borderRadius: 32,
    alignSelf: 'flex-start',
  },
  configButtonContent: {
    height: 'auto',
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  configButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '400',
    marginHorizontal: 0,
    marginVertical: 0,
    lineHeight: 16,
  },
  section: {
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    color: '#6F867D',
    marginBottom: 8,
    lineHeight: 19.2,
    fontWeight: '400',
  },
  chartCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#0D3D32',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 4,
  },
  chartHeader: {
    marginBottom: 16,
  },
  chartAmount: {
    fontSize: 24,
    fontWeight: '500',
    color: '#131615',
    marginBottom: 4,
    lineHeight: 28.8,
  },
  chartSubtext: {
    fontSize: 16,
    color: '#8A9E96',
    lineHeight: 19.2,
  },
  chartContainer: {
    height: 160,
    position: 'relative',
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  chartLegend: {
    flexDirection: 'row',
    gap: 16,
    marginTop: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  legendText: {
    fontSize: 12,
    lineHeight: 14.4,
  },
  bitcoinCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#0D3D32',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 4,
  },
  bitcoinLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  bitcoinIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  bitcoinTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#131615',
    marginBottom: 4,
    lineHeight: 19.2,
  },
  bitcoinSymbol: {
    fontSize: 12,
    color: '#8A9E96',
    lineHeight: 14.4,
  },
  bitcoinRight: {
    alignItems: 'flex-end',
    gap: 4,
  },
  bitcoinValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#131615',
    lineHeight: 19.2,
  },
  bitcoinChange: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  bitcoinChangeText: {
    fontSize: 12,
    color: '#24D327',
    lineHeight: 14.4,
  },
  arrowIconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  transactionsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#0D3D32',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 4,
  },
  transactionsList: {
    marginBottom: 16,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  transactionIcon: {
    width: 40,
    height: 40,
  },
  transactionInfo: {
    gap: 2,
  },
  transactionType: {
    fontSize: 16,
    fontWeight: '500',
    color: '#131615',
    lineHeight: 19.2,
  },
  transactionDate: {
    fontSize: 14,
    fontWeight: '400',
    color: '#8A9E96',
    lineHeight: 16.8,
  },
  transactionRight: {
    alignItems: 'flex-end',
    gap: 2,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '500',
    color: '#131615',
    lineHeight: 19.2,
  },
  transactionBtc: {
    fontSize: 14,
    fontWeight: '400',
    color: '#8A9E96',
    lineHeight: 16.8,
  },
  transactionDivider: {
    height: 1,
    backgroundColor: '#F9FAFA',
    marginVertical: 8,
  },
  seeAllButton: {
    backgroundColor: '#A6B5AF',
    borderRadius: 32,
    alignItems: 'center',
  },
  seeAllButtonContent: {
    height: 'auto',
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  seeAllText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '400',
    marginHorizontal: 0,
    marginVertical: 0,
    lineHeight: 16,
  },
  bottomSpacing: {
    height: 100,
  },
  verticalLinesContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    zIndex: 0,
  },
  dashedLineContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dash: {
    width: 4,
    height: 1,
    backgroundColor: '#DEE3E1',
  },
})
